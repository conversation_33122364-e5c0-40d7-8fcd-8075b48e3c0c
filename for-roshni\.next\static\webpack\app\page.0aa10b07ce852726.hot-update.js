"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/LandingPage.tsx":
/*!****************************************!*\
  !*** ./src/components/LandingPage.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Dynamically import the 3D card to avoid SSR issues\nconst FloatingBirthdayCard = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_FloatingBirthdayCard_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./FloatingBirthdayCard */ \"(app-pages-browser)/./src/components/FloatingBirthdayCard.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\LandingPage.tsx -> \" + \"./FloatingBirthdayCard\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c = FloatingBirthdayCard;\nconst LandingPage = ()=>{\n    _s();\n    const [currentText, setCurrentText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showCursor, setShowCursor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasInteracted, setHasInteracted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMainContent, setShowMainContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start start\",\n            \"end start\"\n        ]\n    });\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        -200\n    ]);\n    const opacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n        0,\n        0.5\n    ], [\n        1,\n        0\n    ]);\n    const messages = [\n        \"Hi Roshni... 💖\",\n        \"Happy Birthday, My Love... 🎂\",\n        \"I made this just for you.\",\n        \"A world where only our love exists...\",\n        \"Click anywhere and step into the magic.\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (currentIndex < messages.length) {\n                const message = messages[currentIndex];\n                let charIndex = 0;\n                const typeInterval = setInterval({\n                    \"LandingPage.useEffect.typeInterval\": ()=>{\n                        if (charIndex <= message.length) {\n                            setCurrentText(message.slice(0, charIndex));\n                            charIndex++;\n                        } else {\n                            clearInterval(typeInterval);\n                            setTimeout({\n                                \"LandingPage.useEffect.typeInterval\": ()=>{\n                                    if (currentIndex < messages.length - 1) {\n                                        setCurrentIndex(currentIndex + 1);\n                                        setCurrentText('');\n                                    } else {\n                                        setShowMainContent(true);\n                                    }\n                                }\n                            }[\"LandingPage.useEffect.typeInterval\"], 2000);\n                        }\n                    }\n                }[\"LandingPage.useEffect.typeInterval\"], 80);\n                return ({\n                    \"LandingPage.useEffect\": ()=>clearInterval(typeInterval)\n                })[\"LandingPage.useEffect\"];\n            }\n        }\n    }[\"LandingPage.useEffect\"], [\n        currentIndex\n    ]);\n    const handleInteraction = ()=>{\n        if (!hasInteracted) {\n            setHasInteracted(true);\n        // Trigger audio or other effects here\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            const cursorInterval = setInterval({\n                \"LandingPage.useEffect.cursorInterval\": ()=>{\n                    setShowCursor({\n                        \"LandingPage.useEffect.cursorInterval\": (prev)=>!prev\n                    }[\"LandingPage.useEffect.cursorInterval\"]);\n                }\n            }[\"LandingPage.useEffect.cursorInterval\"], 500);\n            return ({\n                \"LandingPage.useEffect\": ()=>clearInterval(cursorInterval)\n            })[\"LandingPage.useEffect\"];\n        }\n    }[\"LandingPage.useEffect\"], []);\n    const scrollToNext = ()=>{\n        const nextSection = document.getElementById('celebration');\n        if (nextSection) {\n            nextSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        id: \"landing\",\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        onClick: handleInteraction,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-radial from-pink-500/20 via-purple-500/10 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            ...Array(100)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute w-1 h-1 bg-white rounded-full\",\n                                style: {\n                                    left: \"\".concat((i * 17 + 23) % 100, \"%\"),\n                                    top: \"\".concat((i * 23 + 17) % 100, \"%\")\n                                },\n                                animate: {\n                                    opacity: [\n                                        0.3,\n                                        1,\n                                        0.3\n                                    ],\n                                    scale: [\n                                        0.5,\n                                        1.2,\n                                        0.5\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2 + i % 3,\n                                    repeat: Infinity,\n                                    delay: i * 0.05\n                                }\n                            }, \"star-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children: [\n                            ...Array(15)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute\",\n                                style: {\n                                    left: \"\".concat((i * 13 + 31) % 100, \"%\"),\n                                    top: \"\".concat((i * 19 + 41) % 100, \"%\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -100,\n                                        -200\n                                    ],\n                                    x: [\n                                        0,\n                                        i % 2 === 0 ? 50 : -50,\n                                        0\n                                    ],\n                                    rotate: [\n                                        0,\n                                        360,\n                                        720\n                                    ],\n                                    opacity: [\n                                        0,\n                                        0.6,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 8 + i % 4,\n                                    repeat: Infinity,\n                                    delay: i * 0.8,\n                                    ease: \"easeInOut\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-6 rounded-full blur-sm\",\n                                    style: {\n                                        background: \"linear-gradient(45deg, #ff6b9d, #f368e0)\",\n                                        transform: \"rotate(\".concat(i * 45, \"deg)\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"petal-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children: [\n                            ...Array(12)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute text-pink-400\",\n                                style: {\n                                    left: \"\".concat((i * 29 + 37) % 100, \"%\"),\n                                    top: \"\".concat((i * 31 + 43) % 100, \"%\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -60,\n                                        0\n                                    ],\n                                    scale: [\n                                        0.5,\n                                        1.5,\n                                        0.5\n                                    ],\n                                    opacity: [\n                                        0.2,\n                                        0.8,\n                                        0.2\n                                    ]\n                                },\n                                transition: {\n                                    duration: 6 + i % 3,\n                                    repeat: Infinity,\n                                    delay: i * 0.5\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 20 + i % 3 * 10,\n                                    fill: \"currentColor\",\n                                    className: \"drop-shadow-lg filter blur-[0.5px]\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"glow-heart-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children: [\n                            ...Array(20)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute text-yellow-300\",\n                                style: {\n                                    left: \"\".concat((i * 11 + 47) % 100, \"%\"),\n                                    top: \"\".concat((i * 13 + 53) % 100, \"%\")\n                                },\n                                animate: {\n                                    scale: [\n                                        0,\n                                        1,\n                                        0\n                                    ],\n                                    rotate: [\n                                        0,\n                                        180,\n                                        360\n                                    ],\n                                    opacity: [\n                                        0,\n                                        1,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3,\n                                    repeat: Infinity,\n                                    delay: i * 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 8 + i % 2 * 4\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"sparkle-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                style: {\n                    y,\n                    opacity\n                },\n                className: \"absolute right-10 top-1/2 transform -translate-y-1/2 hidden lg:block\",\n                initial: {\n                    opacity: 0,\n                    x: 100\n                },\n                animate: {\n                    opacity: 0.6,\n                    x: 0\n                },\n                transition: {\n                    duration: 2,\n                    delay: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-64 h-96 bg-gradient-to-b from-pink-500/30 to-purple-500/30 rounded-full blur-sm\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-b from-pink-400/20 to-purple-400/20 rounded-full blur-xl scale-110\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined),\n                        [\n                            ...Array(6)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute w-2 h-8 bg-gradient-to-t from-yellow-400 to-orange-300 rounded-sm\",\n                                style: {\n                                    left: \"\".concat(20 + Math.cos(i * 60 * Math.PI / 180) * 80, \"px\"),\n                                    top: \"\".concat(50 + Math.sin(i * 60 * Math.PI / 180) * 80, \"px\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -10,\n                                        0\n                                    ],\n                                    opacity: [\n                                        0.7,\n                                        1,\n                                        0.7\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2 + i % 2,\n                                    repeat: Infinity,\n                                    delay: i * 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-2 left-1/2 transform -translate-x-1/2 w-1 h-2 bg-orange-400 rounded-full blur-[1px]\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"candle-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined),\n            showMainContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingBirthdayCard, {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 255,\n                columnNumber: 27\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center z-20 px-4 max-w-4xl mx-auto relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-[200px] flex items-center justify-center mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                delay: 0.5\n                            },\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-500/20 blur-3xl rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10 text-2xl md:text-4xl font-medium text-white leading-relaxed\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"inline-block\",\n                                        style: {\n                                            textShadow: '0 0 20px rgba(236, 72, 153, 0.5)'\n                                        },\n                                        children: [\n                                            currentText,\n                                            showCursor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                                                animate: {\n                                                    opacity: [\n                                                        1,\n                                                        0,\n                                                        1\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    repeat: Infinity\n                                                },\n                                                className: \"inline-block w-1 h-8 bg-pink-400 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, currentIndex, true, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, undefined),\n                    showMainContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.8\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        transition: {\n                            duration: 1.5,\n                            ease: \"easeOut\"\n                        },\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h1, {\n                                className: \"text-6xl md:text-8xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-pink-400 via-purple-400 to-rose-400 mb-8 leading-tight\",\n                                animate: {\n                                    backgroundPosition: [\n                                        '0% 50%',\n                                        '100% 50%',\n                                        '0% 50%'\n                                    ]\n                                },\n                                transition: {\n                                    duration: 8,\n                                    repeat: Infinity,\n                                    ease: \"linear\"\n                                },\n                                style: {\n                                    backgroundSize: '200% 200%',\n                                    textShadow: '0 0 40px rgba(236, 72, 153, 0.3)'\n                                },\n                                children: \"Happy Birthday\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h2, {\n                                className: \"text-4xl md:text-6xl font-dancing-script text-pink-300 mb-6\",\n                                animate: {\n                                    textShadow: [\n                                        '0 0 20px rgba(236, 72, 153, 0.5)',\n                                        '0 0 40px rgba(236, 72, 153, 0.8)',\n                                        '0 0 20px rgba(236, 72, 153, 0.5)'\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3,\n                                    repeat: Infinity\n                                },\n                                children: \"Roshni Jwala\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 1\n                                },\n                                className: \"text-lg md:text-xl text-pink-200 max-w-2xl mx-auto leading-relaxed\",\n                                children: \"My Beautiful Babu, My Everything ✨\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, undefined),\n                    !hasInteracted && showMainContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 2\n                        },\n                        className: \"absolute bottom-32 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            animate: {\n                                scale: [\n                                    1,\n                                    1.1,\n                                    1\n                                ],\n                                opacity: [\n                                    0.7,\n                                    1,\n                                    0.7\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity\n                            },\n                            className: \"bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-pink-200 text-sm font-medium\",\n                                children: \"Click anywhere to begin the magic ✨\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, undefined),\n                    showMainContent && hasInteracted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 3\n                        },\n                        className: \"absolute bottom-16 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: scrollToNext,\n                            className: \"group flex flex-col items-center text-pink-300 hover:text-pink-200 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium mb-2\",\n                                    children: \"Enter Your Birthday World\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            10,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    },\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 28,\n                                            className: \"group-hover:scale-110 transition-transform\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-pink-400/30 rounded-full blur-lg scale-150\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-black/30\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-black/40\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"absolute top-1/4 left-1/4 w-32 h-32 rounded-full bg-gradient-to-r from-pink-400/20 to-purple-400/20 blur-3xl\",\n                animate: {\n                    scale: [\n                        1,\n                        1.5,\n                        1\n                    ],\n                    opacity: [\n                        0.3,\n                        0.6,\n                        0.3\n                    ]\n                },\n                transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 400,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LandingPage, \"LyT8IY2gNQGSe7Wtskh1kocH5aw=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform\n    ];\n});\n_c1 = LandingPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LandingPage);\nvar _c, _c1;\n$RefreshReg$(_c, \"FloatingBirthdayCard\");\n$RefreshReg$(_c1, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LandingPage.tsx\n"));

/***/ })

});