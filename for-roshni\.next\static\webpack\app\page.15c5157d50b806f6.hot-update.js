"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/LandingPage.tsx":
/*!****************************************!*\
  !*** ./src/components/LandingPage.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Dynamically import the 3D card to avoid SSR issues\nconst FloatingBirthdayCard = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_c = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_FloatingBirthdayCard_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./FloatingBirthdayCard */ \"(app-pages-browser)/./src/components/FloatingBirthdayCard.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\LandingPage.tsx -> \" + \"./FloatingBirthdayCard\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c1 = FloatingBirthdayCard;\nconst LandingPage = ()=>{\n    _s();\n    const [currentText, setCurrentText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showCursor, setShowCursor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasInteracted, setHasInteracted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMainContent, setShowMainContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start start\",\n            \"end start\"\n        ]\n    });\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        -200\n    ]);\n    const opacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n        0,\n        0.5\n    ], [\n        1,\n        0\n    ]);\n    const messages = [\n        \"Hi Roshni... 💖\",\n        \"Happy Birthday, My Love... 🎂\",\n        \"I made this just for you.\",\n        \"A world where only our love exists...\",\n        \"Click anywhere and step into the magic.\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (currentIndex < messages.length) {\n                const message = messages[currentIndex];\n                let charIndex = 0;\n                const typeInterval = setInterval({\n                    \"LandingPage.useEffect.typeInterval\": ()=>{\n                        if (charIndex <= message.length) {\n                            setCurrentText(message.slice(0, charIndex));\n                            charIndex++;\n                        } else {\n                            clearInterval(typeInterval);\n                            setTimeout({\n                                \"LandingPage.useEffect.typeInterval\": ()=>{\n                                    if (currentIndex < messages.length - 1) {\n                                        setCurrentIndex(currentIndex + 1);\n                                        setCurrentText('');\n                                    } else {\n                                        setShowMainContent(true);\n                                    }\n                                }\n                            }[\"LandingPage.useEffect.typeInterval\"], 2000);\n                        }\n                    }\n                }[\"LandingPage.useEffect.typeInterval\"], 80);\n                return ({\n                    \"LandingPage.useEffect\": ()=>clearInterval(typeInterval)\n                })[\"LandingPage.useEffect\"];\n            }\n        }\n    }[\"LandingPage.useEffect\"], [\n        currentIndex\n    ]);\n    const handleInteraction = ()=>{\n        if (!hasInteracted) {\n            setHasInteracted(true);\n        // Trigger audio or other effects here\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            const cursorInterval = setInterval({\n                \"LandingPage.useEffect.cursorInterval\": ()=>{\n                    setShowCursor({\n                        \"LandingPage.useEffect.cursorInterval\": (prev)=>!prev\n                    }[\"LandingPage.useEffect.cursorInterval\"]);\n                }\n            }[\"LandingPage.useEffect.cursorInterval\"], 500);\n            return ({\n                \"LandingPage.useEffect\": ()=>clearInterval(cursorInterval)\n            })[\"LandingPage.useEffect\"];\n        }\n    }[\"LandingPage.useEffect\"], []);\n    const scrollToNext = ()=>{\n        const nextSection = document.getElementById('celebration');\n        if (nextSection) {\n            nextSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        id: \"landing\",\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        onClick: handleInteraction,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-radial from-pink-500/20 via-purple-500/10 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            ...Array(100)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute w-1 h-1 bg-white rounded-full\",\n                                style: {\n                                    left: \"\".concat((i * 17 + 23) % 100, \"%\"),\n                                    top: \"\".concat((i * 23 + 17) % 100, \"%\")\n                                },\n                                animate: {\n                                    opacity: [\n                                        0.3,\n                                        1,\n                                        0.3\n                                    ],\n                                    scale: [\n                                        0.5,\n                                        1.2,\n                                        0.5\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2 + i % 3,\n                                    repeat: Infinity,\n                                    delay: i * 0.05\n                                }\n                            }, \"star-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children: [\n                            ...Array(15)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute\",\n                                style: {\n                                    left: \"\".concat((i * 13 + 31) % 100, \"%\"),\n                                    top: \"\".concat((i * 19 + 41) % 100, \"%\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -100,\n                                        -200\n                                    ],\n                                    x: [\n                                        0,\n                                        i % 2 === 0 ? 50 : -50,\n                                        0\n                                    ],\n                                    rotate: [\n                                        0,\n                                        360,\n                                        720\n                                    ],\n                                    opacity: [\n                                        0,\n                                        0.6,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 8 + i % 4,\n                                    repeat: Infinity,\n                                    delay: i * 0.8,\n                                    ease: \"easeInOut\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-6 rounded-full blur-sm\",\n                                    style: {\n                                        background: \"linear-gradient(45deg, #ff6b9d, #f368e0)\",\n                                        transform: \"rotate(\".concat(i * 45, \"deg)\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"petal-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children: [\n                            ...Array(12)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute text-pink-400\",\n                                style: {\n                                    left: \"\".concat((i * 29 + 37) % 100, \"%\"),\n                                    top: \"\".concat((i * 31 + 43) % 100, \"%\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -60,\n                                        0\n                                    ],\n                                    scale: [\n                                        0.5,\n                                        1.5,\n                                        0.5\n                                    ],\n                                    opacity: [\n                                        0.2,\n                                        0.8,\n                                        0.2\n                                    ]\n                                },\n                                transition: {\n                                    duration: 6 + i % 3,\n                                    repeat: Infinity,\n                                    delay: i * 0.5\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 20 + i % 3 * 10,\n                                    fill: \"currentColor\",\n                                    className: \"drop-shadow-lg filter blur-[0.5px]\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"glow-heart-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children: [\n                            ...Array(20)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute text-yellow-300\",\n                                style: {\n                                    left: \"\".concat((i * 11 + 47) % 100, \"%\"),\n                                    top: \"\".concat((i * 13 + 53) % 100, \"%\")\n                                },\n                                animate: {\n                                    scale: [\n                                        0,\n                                        1,\n                                        0\n                                    ],\n                                    rotate: [\n                                        0,\n                                        180,\n                                        360\n                                    ],\n                                    opacity: [\n                                        0,\n                                        1,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3,\n                                    repeat: Infinity,\n                                    delay: i * 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 8 + i % 2 * 4\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"sparkle-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                style: {\n                    y,\n                    opacity\n                },\n                className: \"absolute right-10 top-1/2 transform -translate-y-1/2 hidden lg:block\",\n                initial: {\n                    opacity: 0,\n                    x: 100\n                },\n                animate: {\n                    opacity: 0.6,\n                    x: 0\n                },\n                transition: {\n                    duration: 2,\n                    delay: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-64 h-96 bg-gradient-to-b from-pink-500/30 to-purple-500/30 rounded-full blur-sm\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-b from-pink-400/20 to-purple-400/20 rounded-full blur-xl scale-110\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined),\n                        [\n                            ...Array(6)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute w-2 h-8 bg-gradient-to-t from-yellow-400 to-orange-300 rounded-sm\",\n                                style: {\n                                    left: \"\".concat(20 + Math.cos(i * 60 * Math.PI / 180) * 80, \"px\"),\n                                    top: \"\".concat(50 + Math.sin(i * 60 * Math.PI / 180) * 80, \"px\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -10,\n                                        0\n                                    ],\n                                    opacity: [\n                                        0.7,\n                                        1,\n                                        0.7\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2 + i % 2,\n                                    repeat: Infinity,\n                                    delay: i * 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-2 left-1/2 transform -translate-x-1/2 w-1 h-2 bg-orange-400 rounded-full blur-[1px]\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"candle-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center z-20 px-4 max-w-4xl mx-auto relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-[200px] flex items-center justify-center mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                delay: 0.5\n                            },\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-500/20 blur-3xl rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10 text-2xl md:text-4xl font-medium text-white leading-relaxed\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"inline-block\",\n                                        style: {\n                                            textShadow: '0 0 20px rgba(236, 72, 153, 0.5)'\n                                        },\n                                        children: [\n                                            currentText,\n                                            showCursor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                                                animate: {\n                                                    opacity: [\n                                                        1,\n                                                        0,\n                                                        1\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    repeat: Infinity\n                                                },\n                                                className: \"inline-block w-1 h-8 bg-pink-400 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, currentIndex, true, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, undefined),\n                    showMainContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.8\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        transition: {\n                            duration: 1.5,\n                            ease: \"easeOut\"\n                        },\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h1, {\n                                className: \"text-6xl md:text-8xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-pink-400 via-purple-400 to-rose-400 mb-8 leading-tight\",\n                                animate: {\n                                    backgroundPosition: [\n                                        '0% 50%',\n                                        '100% 50%',\n                                        '0% 50%'\n                                    ]\n                                },\n                                transition: {\n                                    duration: 8,\n                                    repeat: Infinity,\n                                    ease: \"linear\"\n                                },\n                                style: {\n                                    backgroundSize: '200% 200%',\n                                    textShadow: '0 0 40px rgba(236, 72, 153, 0.3)'\n                                },\n                                children: \"Happy Birthday\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h2, {\n                                className: \"text-4xl md:text-6xl font-dancing-script text-pink-300 mb-6\",\n                                animate: {\n                                    textShadow: [\n                                        '0 0 20px rgba(236, 72, 153, 0.5)',\n                                        '0 0 40px rgba(236, 72, 153, 0.8)',\n                                        '0 0 20px rgba(236, 72, 153, 0.5)'\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3,\n                                    repeat: Infinity\n                                },\n                                children: \"Roshni Jwala\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 1\n                                },\n                                className: \"text-lg md:text-xl text-pink-200 max-w-2xl mx-auto leading-relaxed\",\n                                children: \"My Beautiful Babu, My Everything ✨\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, undefined),\n                    !hasInteracted && showMainContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 2\n                        },\n                        className: \"absolute bottom-32 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            animate: {\n                                scale: [\n                                    1,\n                                    1.1,\n                                    1\n                                ],\n                                opacity: [\n                                    0.7,\n                                    1,\n                                    0.7\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity\n                            },\n                            className: \"bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-pink-200 text-sm font-medium\",\n                                children: \"Click anywhere to begin the magic ✨\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, undefined),\n                    showMainContent && hasInteracted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 3\n                        },\n                        className: \"absolute bottom-16 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: scrollToNext,\n                            className: \"group flex flex-col items-center text-pink-300 hover:text-pink-200 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium mb-2\",\n                                    children: \"Enter Your Birthday World\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            10,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    },\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 28,\n                                            className: \"group-hover:scale-110 transition-transform\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-pink-400/30 rounded-full blur-lg scale-150\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-black/30\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-black/40\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"absolute top-1/4 left-1/4 w-32 h-32 rounded-full bg-gradient-to-r from-pink-400/20 to-purple-400/20 blur-3xl\",\n                animate: {\n                    scale: [\n                        1,\n                        1.5,\n                        1\n                    ],\n                    opacity: [\n                        0.3,\n                        0.6,\n                        0.3\n                    ]\n                },\n                transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LandingPage, \"LyT8IY2gNQGSe7Wtskh1kocH5aw=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform\n    ];\n});\n_c2 = LandingPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LandingPage);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"FloatingBirthdayCard$dynamic\");\n$RefreshReg$(_c1, \"FloatingBirthdayCard\");\n$RefreshReg$(_c2, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LandingPage.tsx\n"));

/***/ })

});