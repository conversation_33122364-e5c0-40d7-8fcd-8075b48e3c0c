"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_LandingPage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LandingPage */ \"(app-pages-browser)/./src/components/LandingPage.tsx\");\n/* harmony import */ var _components_CelebrationRoom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CelebrationRoom */ \"(app-pages-browser)/./src/components/CelebrationRoom.tsx\");\n/* harmony import */ var _components_LoveStoryTimeline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/LoveStoryTimeline */ \"(app-pages-browser)/./src/components/LoveStoryTimeline.tsx\");\n/* harmony import */ var _components_LoveLetter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/LoveLetter */ \"(app-pages-browser)/./src/components/LoveLetter.tsx\");\n/* harmony import */ var _components_SpinTheWheel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/SpinTheWheel */ \"(app-pages-browser)/./src/components/SpinTheWheel.tsx\");\n/* harmony import */ var _components_PhotoGallery__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/PhotoGallery */ \"(app-pages-browser)/./src/components/PhotoGallery.tsx\");\n/* harmony import */ var _components_VirtualHug__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/VirtualHug */ \"(app-pages-browser)/./src/components/VirtualHug.tsx\");\n/* harmony import */ var _components_FutureDreams__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/FutureDreams */ \"(app-pages-browser)/./src/components/FutureDreams.tsx\");\n/* harmony import */ var _components_FloatingNavigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/FloatingNavigation */ \"(app-pages-browser)/./src/components/FloatingNavigation.tsx\");\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/LoadingScreen */ \"(app-pages-browser)/./src/components/LoadingScreen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const handleLoadingComplete = ()=>{\n        setIsLoading(false);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            onLoadingComplete: handleLoadingComplete\n        }, void 0, false, {\n            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 24,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LandingPage__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CelebrationRoom__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoveStoryTimeline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoveLetter__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SpinTheWheel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PhotoGallery__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VirtualHug__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FutureDreams__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FloatingNavigation__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"5S7VQ8+9ArWv2AFPIfnY+LwrHeg=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});