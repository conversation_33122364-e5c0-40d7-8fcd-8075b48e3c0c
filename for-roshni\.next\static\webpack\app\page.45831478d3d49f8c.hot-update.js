"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/PhotoGallery.tsx":
/*!*****************************************!*\
  !*** ./src/components/PhotoGallery.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Heart,Pause,Play,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Heart,Pause,Play,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Heart,Pause,Play,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Heart,Pause,Play,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Heart,Pause,Play,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst PhotoGallery = ()=>{\n    _s();\n    const [selectedPhoto, setSelectedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSlideshow, setIsSlideshow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSlideIndex, setCurrentSlideIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Placeholder photos - in a real implementation, these would be actual photos\n    const photos = [\n        {\n            id: 1,\n            src: \"/api/placeholder/300/400\",\n            caption: \"Cutest Smile\",\n            description: \"This is my favorite photo of you, Roshni. Your smile here just melts my heart every single time I look at it. ❤️\",\n            date: \"A beautiful day\"\n        },\n        {\n            id: 2,\n            src: \"/api/placeholder/300/400\",\n            caption: \"Best Hug\",\n            description: \"The warmest, most comforting hug ever. I felt so safe and loved in this moment with you, my beautiful babu. 🤗\",\n            date: \"Perfect moment\"\n        },\n        {\n            id: 3,\n            src: \"/api/placeholder/300/400\",\n            caption: \"My Princess\",\n            description: \"You look absolutely stunning here! Like a real princess, which you are to me every single day. 👑✨\",\n            date: \"Magical evening\"\n        },\n        {\n            id: 4,\n            src: \"/api/placeholder/300/400\",\n            caption: \"Silly Us\",\n            description: \"I love how goofy we can be together. These silly moments are some of my most treasured memories with you! 😄\",\n            date: \"Fun times\"\n        },\n        {\n            id: 5,\n            src: \"/api/placeholder/300/400\",\n            caption: \"Beautiful Eyes\",\n            description: \"Your eyes in this photo... I could get lost in them forever. They hold so much love, warmth, and beauty. 👀💕\",\n            date: \"Dreamy afternoon\"\n        },\n        {\n            id: 6,\n            src: \"/api/placeholder/300/400\",\n            caption: \"Together Forever\",\n            description: \"This photo represents everything I want - you and me, together, happy, and in love. Forever and always. 💑\",\n            date: \"Our future\"\n        }\n    ];\n    const startSlideshow = ()=>{\n        setIsSlideshow(true);\n        setCurrentSlideIndex(0);\n    };\n    const stopSlideshow = ()=>{\n        setIsSlideshow(false);\n    };\n    const nextSlide = ()=>{\n        setCurrentSlideIndex((prev)=>(prev + 1) % photos.length);\n    };\n    const prevSlide = ()=>{\n        setCurrentSlideIndex((prev)=>(prev - 1 + photos.length) % photos.length);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"gallery\",\n        className: \"min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 py-16 px-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-orange-600 to-red-600 mb-4\",\n                                children: \"Our Beautiful Memories\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-2xl mx-auto mb-8\",\n                                children: \"Every photo tells a story of our love, Roshni \\uD83D\\uDCF8\\uD83D\\uDC95\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                onClick: startSlideshow,\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-3 rounded-full hover:from-orange-600 hover:to-red-600 transition-all font-medium flex items-center space-x-2 mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Start Slideshow\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: photos.map((photo, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.1\n                                },\n                                whileHover: {\n                                    y: -10,\n                                    rotate: Math.random() * 6 - 3\n                                },\n                                className: \"cursor-pointer\",\n                                onClick: ()=>setSelectedPhoto(photo),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-4 pb-16 shadow-xl transform rotate-1 hover:rotate-0 transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-hidden bg-gray-200 aspect-[3/4] mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full h-full bg-gradient-to-br from-pink-200 to-purple-200 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-16 h-16 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black/0 hover:bg-black/20 transition-all duration-300 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-8 h-8 text-white opacity-0 hover:opacity-100 transition-opacity\",\n                                                        fill: \"currentColor\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-dancing-script text-xl text-gray-800 mb-1\",\n                                                    children: photo.caption\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: photo.date\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, photo.id, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: selectedPhoto && !isSlideshow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4\",\n                    onClick: ()=>setSelectedPhoto(null),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            scale: 0.5,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.5,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-2xl p-8 max-w-2xl mx-auto shadow-2xl\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-dancing-script text-gray-800\",\n                                        children: selectedPhoto.caption\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedPhoto(null),\n                                        className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative overflow-hidden bg-gray-200 aspect-[3/4] mb-6 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-full bg-gradient-to-br from-pink-200 to-purple-200 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-24 h-24 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 leading-relaxed mb-4\",\n                                children: selectedPhoto.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-orange-500 font-medium text-center\",\n                                children: selectedPhoto.date\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: isSlideshow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black flex items-center justify-center z-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-8 right-8 flex space-x-4 z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: stopSlideshow,\n                                    className: \"bg-white/20 backdrop-blur-sm rounded-full p-3 text-white hover:bg-white/30 transition-all\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: stopSlideshow,\n                                    className: \"bg-white/20 backdrop-blur-sm rounded-full p-3 text-white hover:bg-white/30 transition-all\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: prevSlide,\n                            className: \"absolute left-8 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm rounded-full p-4 text-white hover:bg-white/30 transition-all z-10\",\n                            children: \"←\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: nextSlide,\n                            className: \"absolute right-8 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm rounded-full p-4 text-white hover:bg-white/30 transition-all z-10\",\n                            children: \"→\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-white max-w-4xl mx-auto px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 100\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    x: -100\n                                },\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative overflow-hidden bg-gray-800 aspect-[3/4] max-h-96 mx-auto mb-6 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-br from-pink-300 to-purple-300 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-24 h-24 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-4xl font-dancing-script mb-4\",\n                                        children: photos[currentSlideIndex].caption\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg leading-relaxed mb-4 max-w-2xl mx-auto\",\n                                        children: photos[currentSlideIndex].description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-orange-300 font-medium\",\n                                        children: photos[currentSlideIndex].date\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, currentSlideIndex, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2\",\n                            children: photos.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentSlideIndex(index),\n                                    className: \"w-3 h-3 rounded-full transition-all \".concat(index === currentSlideIndex ? 'bg-white' : 'bg-white/40')\n                                }, index, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PhotoGallery, \"/YS6bqu/OM1T3qPs/fdUpPZ1K3A=\");\n_c = PhotoGallery;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PhotoGallery);\nvar _c;\n$RefreshReg$(_c, \"PhotoGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PhotoGallery.tsx\n"));

/***/ })

});