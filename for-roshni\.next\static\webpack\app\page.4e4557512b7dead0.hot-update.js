"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/LandingPage.tsx":
/*!****************************************!*\
  !*** ./src/components/LandingPage.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst LandingPage = ()=>{\n    _s();\n    const [currentText, setCurrentText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showCursor, setShowCursor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasInteracted, setHasInteracted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMainContent, setShowMainContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start start\",\n            \"end start\"\n        ]\n    });\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        -200\n    ]);\n    const opacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        0.5\n    ], [\n        1,\n        0\n    ]);\n    const messages = [\n        \"Hi Roshni... 💖\",\n        \"Happy Birthday, My Love... 🎂\",\n        \"I made this just for you.\",\n        \"A world where only our love exists...\",\n        \"Click anywhere and step into the magic.\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (currentIndex < messages.length) {\n                const message = messages[currentIndex];\n                let charIndex = 0;\n                const typeInterval = setInterval({\n                    \"LandingPage.useEffect.typeInterval\": ()=>{\n                        if (charIndex <= message.length) {\n                            setCurrentText(message.slice(0, charIndex));\n                            charIndex++;\n                        } else {\n                            clearInterval(typeInterval);\n                            setTimeout({\n                                \"LandingPage.useEffect.typeInterval\": ()=>{\n                                    if (currentIndex < messages.length - 1) {\n                                        setCurrentIndex(currentIndex + 1);\n                                        setCurrentText('');\n                                    } else {\n                                        setShowMainContent(true);\n                                    }\n                                }\n                            }[\"LandingPage.useEffect.typeInterval\"], 2000);\n                        }\n                    }\n                }[\"LandingPage.useEffect.typeInterval\"], 80);\n                return ({\n                    \"LandingPage.useEffect\": ()=>clearInterval(typeInterval)\n                })[\"LandingPage.useEffect\"];\n            }\n        }\n    }[\"LandingPage.useEffect\"], [\n        currentIndex\n    ]);\n    const handleInteraction = ()=>{\n        if (!hasInteracted) {\n            setHasInteracted(true);\n        // Trigger audio or other effects here\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            const cursorInterval = setInterval({\n                \"LandingPage.useEffect.cursorInterval\": ()=>{\n                    setShowCursor({\n                        \"LandingPage.useEffect.cursorInterval\": (prev)=>!prev\n                    }[\"LandingPage.useEffect.cursorInterval\"]);\n                }\n            }[\"LandingPage.useEffect.cursorInterval\"], 500);\n            return ({\n                \"LandingPage.useEffect\": ()=>clearInterval(cursorInterval)\n            })[\"LandingPage.useEffect\"];\n        }\n    }[\"LandingPage.useEffect\"], []);\n    const scrollToNext = ()=>{\n        const nextSection = document.getElementById('celebration');\n        if (nextSection) {\n            nextSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        id: \"landing\",\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        onClick: handleInteraction,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-radial from-pink-500/20 via-purple-500/10 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            ...Array(100)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"absolute w-1 h-1 bg-white rounded-full\",\n                                style: {\n                                    left: \"\".concat((i * 17 + 23) % 100, \"%\"),\n                                    top: \"\".concat((i * 23 + 17) % 100, \"%\")\n                                },\n                                animate: {\n                                    opacity: [\n                                        0.3,\n                                        1,\n                                        0.3\n                                    ],\n                                    scale: [\n                                        0.5,\n                                        1.2,\n                                        0.5\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2 + i % 3,\n                                    repeat: Infinity,\n                                    delay: i * 0.05\n                                }\n                            }, \"star-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children: [\n                            ...Array(15)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"absolute\",\n                                style: {\n                                    left: \"\".concat((i * 13 + 31) % 100, \"%\"),\n                                    top: \"\".concat((i * 19 + 41) % 100, \"%\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -100,\n                                        -200\n                                    ],\n                                    x: [\n                                        0,\n                                        i % 2 === 0 ? 50 : -50,\n                                        0\n                                    ],\n                                    rotate: [\n                                        0,\n                                        360,\n                                        720\n                                    ],\n                                    opacity: [\n                                        0,\n                                        0.6,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 8 + i % 4,\n                                    repeat: Infinity,\n                                    delay: i * 0.8,\n                                    ease: \"easeInOut\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-6 rounded-full blur-sm\",\n                                    style: {\n                                        background: \"linear-gradient(45deg, #ff6b9d, #f368e0)\",\n                                        transform: \"rotate(\".concat(i * 45, \"deg)\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"petal-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children: [\n                            ...Array(12)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"absolute text-pink-400\",\n                                style: {\n                                    left: \"\".concat((i * 29 + 37) % 100, \"%\"),\n                                    top: \"\".concat((i * 31 + 43) % 100, \"%\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -60,\n                                        0\n                                    ],\n                                    scale: [\n                                        0.5,\n                                        1.5,\n                                        0.5\n                                    ],\n                                    opacity: [\n                                        0.2,\n                                        0.8,\n                                        0.2\n                                    ]\n                                },\n                                transition: {\n                                    duration: 6 + i % 3,\n                                    repeat: Infinity,\n                                    delay: i * 0.5\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    size: 20 + i % 3 * 10,\n                                    fill: \"currentColor\",\n                                    className: \"drop-shadow-lg filter blur-[0.5px]\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"glow-heart-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children: [\n                            ...Array(20)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"absolute text-yellow-300\",\n                                style: {\n                                    left: \"\".concat((i * 11 + 47) % 100, \"%\"),\n                                    top: \"\".concat((i * 13 + 53) % 100, \"%\")\n                                },\n                                animate: {\n                                    scale: [\n                                        0,\n                                        1,\n                                        0\n                                    ],\n                                    rotate: [\n                                        0,\n                                        180,\n                                        360\n                                    ],\n                                    opacity: [\n                                        0,\n                                        1,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3,\n                                    repeat: Infinity,\n                                    delay: i * 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 8 + i % 2 * 4\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"sparkle-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                style: {\n                    y,\n                    opacity\n                },\n                className: \"absolute right-10 top-1/2 transform -translate-y-1/2 hidden lg:block\",\n                initial: {\n                    opacity: 0,\n                    x: 100\n                },\n                animate: {\n                    opacity: 0.6,\n                    x: 0\n                },\n                transition: {\n                    duration: 2,\n                    delay: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-64 h-96 bg-gradient-to-b from-pink-500/30 to-purple-500/30 rounded-full blur-sm\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-b from-pink-400/20 to-purple-400/20 rounded-full blur-xl scale-110\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, undefined),\n                        [\n                            ...Array(6)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"absolute w-2 h-8 bg-gradient-to-t from-yellow-400 to-orange-300 rounded-sm\",\n                                style: {\n                                    left: \"\".concat(20 + Math.cos(i * 60 * Math.PI / 180) * 80, \"px\"),\n                                    top: \"\".concat(50 + Math.sin(i * 60 * Math.PI / 180) * 80, \"px\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -10,\n                                        0\n                                    ],\n                                    opacity: [\n                                        0.7,\n                                        1,\n                                        0.7\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2 + i % 2,\n                                    repeat: Infinity,\n                                    delay: i * 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-2 left-1/2 transform -translate-x-1/2 w-1 h-2 bg-orange-400 rounded-full blur-[1px]\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"candle-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center z-20 px-4 max-w-4xl mx-auto relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-[200px] flex items-center justify-center mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                delay: 0.5\n                            },\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-500/20 blur-3xl rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10 text-2xl md:text-4xl font-medium text-white leading-relaxed\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"inline-block\",\n                                        style: {\n                                            textShadow: '0 0 20px rgba(236, 72, 153, 0.5)'\n                                        },\n                                        children: [\n                                            currentText,\n                                            showCursor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                                animate: {\n                                                    opacity: [\n                                                        1,\n                                                        0,\n                                                        1\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    repeat: Infinity\n                                                },\n                                                className: \"inline-block w-1 h-8 bg-pink-400 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, currentIndex, true, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined),\n                    showMainContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.8\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        transition: {\n                            duration: 1.5,\n                            ease: \"easeOut\"\n                        },\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                                className: \"text-6xl md:text-8xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-pink-400 via-purple-400 to-rose-400 mb-8 leading-tight\",\n                                animate: {\n                                    backgroundPosition: [\n                                        '0% 50%',\n                                        '100% 50%',\n                                        '0% 50%'\n                                    ]\n                                },\n                                transition: {\n                                    duration: 8,\n                                    repeat: Infinity,\n                                    ease: \"linear\"\n                                },\n                                style: {\n                                    backgroundSize: '200% 200%',\n                                    textShadow: '0 0 40px rgba(236, 72, 153, 0.3)'\n                                },\n                                children: \"Happy Birthday\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h2, {\n                                className: \"text-4xl md:text-6xl font-dancing-script text-pink-300 mb-6\",\n                                animate: {\n                                    textShadow: [\n                                        '0 0 20px rgba(236, 72, 153, 0.5)',\n                                        '0 0 40px rgba(236, 72, 153, 0.8)',\n                                        '0 0 20px rgba(236, 72, 153, 0.5)'\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3,\n                                    repeat: Infinity\n                                },\n                                children: \"Roshni Jwala\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 1\n                                },\n                                className: \"text-lg md:text-xl text-pink-200 max-w-2xl mx-auto leading-relaxed\",\n                                children: \"My Beautiful Babu, My Everything ✨\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-[120px] flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                delay: 1.5\n                            },\n                            className: \"text-xl md:text-2xl text-gray-600 font-medium\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-block\",\n                                children: [\n                                    currentText,\n                                    showCursor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block w-0.5 h-6 bg-pink-500 ml-1 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, undefined),\n                    currentIndex >= messages.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 2\n                        },\n                        className: \"mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: scrollToNext,\n                            className: \"group flex flex-col items-center text-pink-500 hover:text-pink-600 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"Scroll Down\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            10,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        size: 32,\n                                        className: \"group-hover:scale-110 transition-transform\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: 3\n                },\n                className: \"absolute bottom-8 right-8 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"bg-white/20 backdrop-blur-sm rounded-full p-3 text-pink-500 hover:bg-white/30 transition-all\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        size: 24,\n                        className: \"animate-heartbeat\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 376,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LandingPage, \"LyT8IY2gNQGSe7Wtskh1kocH5aw=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform\n    ];\n});\n_c = LandingPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LandingPage);\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LandingPage.tsx\n"));

/***/ })

});