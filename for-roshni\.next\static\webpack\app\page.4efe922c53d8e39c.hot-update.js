"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/music.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Music)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M9 18V5l12-2v13\",\n            key: \"1jmyc2\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"6\",\n            cy: \"18\",\n            r: \"3\",\n            key: \"fqmcym\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"18\",\n            cy: \"16\",\n            r: \"3\",\n            key: \"1hluhg\"\n        }\n    ]\n];\nconst Music = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"music\", __iconNode);\n //# sourceMappingURL=music.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_LandingPage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LandingPage */ \"(app-pages-browser)/./src/components/LandingPage.tsx\");\n/* harmony import */ var _components_CelebrationRoom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CelebrationRoom */ \"(app-pages-browser)/./src/components/CelebrationRoom.tsx\");\n/* harmony import */ var _components_LoveStoryTimeline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/LoveStoryTimeline */ \"(app-pages-browser)/./src/components/LoveStoryTimeline.tsx\");\n/* harmony import */ var _components_LoveLetter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/LoveLetter */ \"(app-pages-browser)/./src/components/LoveLetter.tsx\");\n/* harmony import */ var _components_SpinTheWheel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/SpinTheWheel */ \"(app-pages-browser)/./src/components/SpinTheWheel.tsx\");\n/* harmony import */ var _components_PhotoGallery__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/PhotoGallery */ \"(app-pages-browser)/./src/components/PhotoGallery.tsx\");\n/* harmony import */ var _components_VirtualHug__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/VirtualHug */ \"(app-pages-browser)/./src/components/VirtualHug.tsx\");\n/* harmony import */ var _components_FutureDreams__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/FutureDreams */ \"(app-pages-browser)/./src/components/FutureDreams.tsx\");\n/* harmony import */ var _components_FloatingNavigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/FloatingNavigation */ \"(app-pages-browser)/./src/components/FloatingNavigation.tsx\");\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/LoadingScreen */ \"(app-pages-browser)/./src/components/LoadingScreen.tsx\");\n/* harmony import */ var _components_AudioManager__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/AudioManager */ \"(app-pages-browser)/./src/components/AudioManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const handleLoadingComplete = ()=>{\n        setIsLoading(false);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            onLoadingComplete: handleLoadingComplete\n        }, void 0, false, {\n            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 24,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LandingPage__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CelebrationRoom__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoveStoryTimeline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoveLetter__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SpinTheWheel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PhotoGallery__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VirtualHug__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FutureDreams__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FloatingNavigation__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioManager__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"5S7VQ8+9ArWv2AFPIfnY+LwrHeg=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AudioManager.tsx":
/*!*****************************************!*\
  !*** ./src/components/AudioManager.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Music_Pause_Play_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Music,Pause,Play,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Music_Pause_Play_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Music,Pause,Play,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Music_Pause_Play_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Music,Pause,Play,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Music_Pause_Play_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Music,Pause,Play,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Music_Pause_Play_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Music,Pause,Play,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst AudioManager = ()=>{\n    _s();\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.3);\n    const [currentTrack, setCurrentTrack] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showControls, setShowControls] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Audio tracks for different sections\n    const tracks = [\n        {\n            id: 'romantic',\n            name: 'Romantic Ambience',\n            src: '/audio/romantic-ambient.mp3',\n            section: 'landing'\n        },\n        {\n            id: 'celebration',\n            name: 'Birthday Celebration',\n            src: '/audio/birthday-celebration.mp3',\n            section: 'celebration'\n        },\n        {\n            id: 'love-story',\n            name: 'Our Love Story',\n            src: '/audio/love-story.mp3',\n            section: 'timeline'\n        },\n        {\n            id: 'tender',\n            name: 'Tender Moments',\n            src: '/audio/tender-moments.mp3',\n            section: 'letter'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioManager.useEffect\": ()=>{\n            // Auto-play romantic music on load (with user interaction)\n            const handleFirstInteraction = {\n                \"AudioManager.useEffect.handleFirstInteraction\": ()=>{\n                    if (!currentTrack) {\n                        setCurrentTrack(tracks[0]);\n                        setIsPlaying(true);\n                    }\n                    document.removeEventListener('click', handleFirstInteraction);\n                    document.removeEventListener('keydown', handleFirstInteraction);\n                }\n            }[\"AudioManager.useEffect.handleFirstInteraction\"];\n            document.addEventListener('click', handleFirstInteraction);\n            document.addEventListener('keydown', handleFirstInteraction);\n            return ({\n                \"AudioManager.useEffect\": ()=>{\n                    document.removeEventListener('click', handleFirstInteraction);\n                    document.removeEventListener('keydown', handleFirstInteraction);\n                }\n            })[\"AudioManager.useEffect\"];\n        }\n    }[\"AudioManager.useEffect\"], [\n        currentTrack\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioManager.useEffect\": ()=>{\n            if (audioRef.current && currentTrack) {\n                audioRef.current.src = currentTrack.src;\n                audioRef.current.volume = isMuted ? 0 : volume;\n                audioRef.current.loop = true;\n                if (isPlaying) {\n                    audioRef.current.play().catch(console.error);\n                } else {\n                    audioRef.current.pause();\n                }\n            }\n        }\n    }[\"AudioManager.useEffect\"], [\n        currentTrack,\n        isPlaying,\n        volume,\n        isMuted\n    ]);\n    const togglePlay = ()=>{\n        setIsPlaying(!isPlaying);\n    };\n    const toggleMute = ()=>{\n        setIsMuted(!isMuted);\n    };\n    const changeTrack = (track)=>{\n        setCurrentTrack(track);\n        setIsPlaying(true);\n    };\n    const handleVolumeChange = (newVolume)=>{\n        setVolume(newVolume);\n        if (audioRef.current) {\n            audioRef.current.volume = isMuted ? 0 : newVolume;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                ref: audioRef,\n                preload: \"metadata\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"fixed top-8 left-8 z-50\",\n                onMouseEnter: ()=>setShowControls(true),\n                onMouseLeave: ()=>setShowControls(false),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                        onClick: togglePlay,\n                        whileHover: {\n                            scale: 1.1\n                        },\n                        whileTap: {\n                            scale: 0.9\n                        },\n                        className: \"w-14 h-14 bg-white/20 backdrop-blur-sm rounded-full shadow-lg flex items-center justify-center text-pink-500 hover:bg-white/30 transition-all\",\n                        children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Music_Pause_Play_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Music_Pause_Play_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                        children: showControls && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            className: \"absolute top-16 left-0 bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-xl min-w-64\",\n                            children: [\n                                currentTrack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Music_Pause_Play_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 text-pink-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: currentTrack.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMute,\n                                                className: \"text-pink-500 hover:text-pink-600 transition-colors\",\n                                                children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Music_Pause_Play_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Music_Pause_Play_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"0\",\n                                                max: \"1\",\n                                                step: \"0.1\",\n                                                value: isMuted ? 0 : volume,\n                                                onChange: (e)=>handleVolumeChange(parseFloat(e.target.value)),\n                                                className: \"flex-1 h-2 bg-pink-200 rounded-lg appearance-none cursor-pointer\",\n                                                style: {\n                                                    background: \"linear-gradient(to right, #ec4899 0%, #ec4899 \".concat((isMuted ? 0 : volume) * 100, \"%, #fce7f3 \").concat((isMuted ? 0 : volume) * 100, \"%, #fce7f3 100%)\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mb-2\",\n                                            children: \"Choose Music:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: tracks.map((track)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>changeTrack(track),\n                                                    className: \"w-full text-left px-3 py-2 rounded-lg text-sm transition-all \".concat((currentTrack === null || currentTrack === void 0 ? void 0 : currentTrack.id) === track.id ? 'bg-pink-100 text-pink-700 font-medium' : 'text-gray-600 hover:bg-gray-100'),\n                                                    children: track.name\n                                                }, track.id, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined),\n            isPlaying && !isMuted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"fixed bottom-8 left-8 z-40\",\n                initial: {\n                    opacity: 0,\n                    scale: 0.8\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                exit: {\n                    opacity: 0,\n                    scale: 0.8\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-end space-x-1\",\n                    children: [\n                        ...Array(5)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"w-1 bg-gradient-to-t from-pink-400 to-purple-400 rounded-full\",\n                            animate: {\n                                height: [\n                                    8,\n                                    20,\n                                    8\n                                ]\n                            },\n                            transition: {\n                                duration: 0.5 + Math.random() * 0.5,\n                                repeat: Infinity,\n                                delay: i * 0.1\n                            }\n                        }, i, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\AudioManager.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AudioManager, \"EJxIEBc+zYxxZVE7wbYe8RgA75Q=\");\n_c = AudioManager;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AudioManager);\nvar _c;\n$RefreshReg$(_c, \"AudioManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AudioManager.tsx\n"));

/***/ })

});