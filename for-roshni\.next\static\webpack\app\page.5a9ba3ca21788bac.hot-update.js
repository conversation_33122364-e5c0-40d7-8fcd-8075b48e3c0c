"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SpinTheWheel.tsx":
/*!*****************************************!*\
  !*** ./src/components/SpinTheWheel.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,RotateCcw,Sparkles,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,RotateCcw,Sparkles,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,RotateCcw,Sparkles,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,RotateCcw,Sparkles,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst SpinTheWheel = ()=>{\n    _s();\n    const [isSpinning, setIsSpinning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentRotation, setCurrentRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedSegment, setSelectedSegment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const wheelSegments = [\n        {\n            id: 1,\n            text: \"Your smile lights up my entire world\",\n            category: \"Reason I love you\",\n            color: \"from-pink-400 to-rose-500\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                lineNumber: 27,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: 2,\n            text: \"The way you scrunch your nose when you laugh\",\n            category: \"Cute thing you do\",\n            color: \"from-purple-400 to-pink-500\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                lineNumber: 34,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: 3,\n            text: \"Our first kiss under the stars\",\n            category: \"Memory I'll never forget\",\n            color: \"from-blue-400 to-purple-500\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                lineNumber: 41,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: 4,\n            text: \"Traveling the world together\",\n            category: \"Dream I have with you\",\n            color: \"from-green-400 to-blue-500\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                lineNumber: 48,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: 5,\n            text: \"You make me want to be a better person\",\n            category: \"Reason I love you\",\n            color: \"from-yellow-400 to-orange-500\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                lineNumber: 55,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: 6,\n            text: \"How you steal my hoodies and look adorable\",\n            category: \"Cute thing you do\",\n            color: \"from-red-400 to-pink-500\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: 7,\n            text: \"That time you fell asleep on my shoulder\",\n            category: \"Memory I'll never forget\",\n            color: \"from-indigo-400 to-purple-500\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                lineNumber: 69,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: 8,\n            text: \"Growing old together and still being silly\",\n            category: \"Dream I have with you\",\n            color: \"from-teal-400 to-blue-500\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                lineNumber: 76,\n                columnNumber: 13\n            }, undefined)\n        }\n    ];\n    const spinWheel = ()=>{\n        if (isSpinning) return;\n        setIsSpinning(true);\n        setShowResult(false);\n        setSelectedSegment(null);\n        // Random rotation between 1440 and 2160 degrees (4-6 full rotations)\n        const randomRotation = 1440 + Math.random() * 720;\n        const newRotation = currentRotation + randomRotation;\n        setCurrentRotation(newRotation);\n        // Calculate which segment was selected\n        const segmentAngle = 360 / wheelSegments.length;\n        const normalizedRotation = newRotation % 360;\n        const selectedIndex = Math.floor((360 - normalizedRotation + segmentAngle / 2) / segmentAngle) % wheelSegments.length;\n        setTimeout(()=>{\n            setIsSpinning(false);\n            setSelectedSegment(wheelSegments[selectedIndex]);\n            setShowResult(true);\n        }, 3000);\n    };\n    const segmentAngle = 360 / wheelSegments.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"wheel\",\n        className: \"min-h-screen bg-gradient-to-br from-violet-50 via-purple-50 to-fuchsia-50 py-16 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h2, {\n                    initial: {\n                        opacity: 0,\n                        y: -50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600 mb-8\",\n                    children: \"Spin the Wheel of Love\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                    initial: {\n                        opacity: 0\n                    },\n                    whileInView: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    className: \"text-xl text-gray-600 mb-12\",\n                    children: \"Discover sweet reasons why I love you, Babu! \\uD83D\\uDC95\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mx-auto mb-12\",\n                    style: {\n                        width: '400px',\n                        height: '400px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            className: \"relative w-full h-full rounded-full shadow-2xl overflow-hidden\",\n                            animate: {\n                                rotate: currentRotation\n                            },\n                            transition: {\n                                duration: isSpinning ? 3 : 0,\n                                ease: isSpinning ? \"easeOut\" : \"linear\"\n                            },\n                            children: wheelSegments.map((segment, index)=>{\n                                const rotation = index * segmentAngle;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute w-full h-full bg-gradient-to-r \".concat(segment.color),\n                                    style: {\n                                        clipPath: \"polygon(50% 50%, 50% 0%, \".concat(50 + 50 * Math.cos(segmentAngle * Math.PI / 180), \"% \").concat(50 - 50 * Math.sin(segmentAngle * Math.PI / 180), \"%)\"),\n                                        transform: \"rotate(\".concat(rotation, \"deg)\"),\n                                        transformOrigin: 'center'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute text-white text-xs font-medium p-2\",\n                                        style: {\n                                            top: '20%',\n                                            left: '60%',\n                                            transform: \"rotate(\".concat(segmentAngle / 2, \"deg)\"),\n                                            transformOrigin: 'left center',\n                                            width: '80px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                segment.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-[10px] leading-tight\",\n                                                    children: segment.category\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, segment.id, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-white rounded-full shadow-lg flex items-center justify-center z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-8 h-8 text-pink-500\",\n                                fill: \"currentColor\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 z-20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-white shadow-lg\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                    onClick: spinWheel,\n                    disabled: isSpinning,\n                    whileHover: {\n                        scale: isSpinning ? 1 : 1.05\n                    },\n                    whileTap: {\n                        scale: isSpinning ? 1 : 0.95\n                    },\n                    className: \"\\n            px-8 py-4 rounded-full text-white font-bold text-lg shadow-lg transition-all duration-300\\n            \".concat(isSpinning ? 'bg-gray-400 cursor-not-allowed' : 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600', \"\\n          \"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-5 h-5 \".concat(isSpinning ? 'animate-spin' : '')\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: isSpinning ? 'Spinning...' : 'Spin the Wheel!'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                    children: showResult && selectedSegment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n                        onClick: ()=>setShowResult(false),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                scale: 0.5,\n                                opacity: 0,\n                                rotateY: -90\n                            },\n                            animate: {\n                                scale: 1,\n                                opacity: 1,\n                                rotateY: 0\n                            },\n                            exit: {\n                                scale: 0.5,\n                                opacity: 0,\n                                rotateY: 90\n                            },\n                            transition: {\n                                type: \"spring\",\n                                damping: 25,\n                                stiffness: 300\n                            },\n                            className: \"bg-white rounded-2xl p-8 max-w-md mx-auto shadow-2xl\",\n                            onClick: (e)=>e.stopPropagation(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 pointer-events-none overflow-hidden rounded-2xl\",\n                                    children: [\n                                        ...Array(20)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            className: \"absolute w-2 h-2 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full\",\n                                            style: {\n                                                left: \"\".concat(Math.random() * 100, \"%\"),\n                                                top: '-10px'\n                                            },\n                                            animate: {\n                                                y: 400,\n                                                rotate: 360,\n                                                x: [\n                                                    0,\n                                                    Math.random() * 100 - 50\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 2,\n                                                ease: \"easeOut\",\n                                                delay: Math.random() * 0.5\n                                            }\n                                        }, i, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 rounded-full bg-gradient-to-r \".concat(selectedSegment.color, \" flex items-center justify-center text-white mx-auto mb-6\"),\n                                            children: selectedSegment.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-dancing-script text-gray-800 mb-2\",\n                                            children: selectedSegment.category\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 leading-relaxed mb-6 text-lg\",\n                                            children: [\n                                                '\"',\n                                                selectedSegment.text,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center space-x-2 mb-6\",\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_RotateCcw_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    className: \"w-4 h-4 text-pink-400 animate-pulse\",\n                                                    fill: \"currentColor\",\n                                                    style: {\n                                                        animationDelay: \"\".concat(i * 0.2, \"s\")\n                                                    }\n                                                }, i, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowResult(false),\n                                            className: \"bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-3 rounded-full hover:from-purple-600 hover:to-pink-600 transition-all font-medium\",\n                                            children: \"Aww, I love you too! ❤️\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\SpinTheWheel.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SpinTheWheel, \"+K8SmJwO0SFAI2CfrkKXEbeqBlM=\");\n_c = SpinTheWheel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SpinTheWheel);\nvar _c;\n$RefreshReg$(_c, \"SpinTheWheel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SpinTheWheel.tsx\n"));

/***/ })

});