"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_LandingPage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LandingPage */ \"(app-pages-browser)/./src/components/LandingPage.tsx\");\n/* harmony import */ var _components_CelebrationRoom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CelebrationRoom */ \"(app-pages-browser)/./src/components/CelebrationRoom.tsx\");\n/* harmony import */ var _components_LoveStoryTimeline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/LoveStoryTimeline */ \"(app-pages-browser)/./src/components/LoveStoryTimeline.tsx\");\n/* harmony import */ var _components_LoveLetter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/LoveLetter */ \"(app-pages-browser)/./src/components/LoveLetter.tsx\");\n/* harmony import */ var _components_SpinTheWheel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/SpinTheWheel */ \"(app-pages-browser)/./src/components/SpinTheWheel.tsx\");\n/* harmony import */ var _components_PhotoGallery__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/PhotoGallery */ \"(app-pages-browser)/./src/components/PhotoGallery.tsx\");\n/* harmony import */ var _components_VirtualHug__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/VirtualHug */ \"(app-pages-browser)/./src/components/VirtualHug.tsx\");\n/* harmony import */ var _components_FutureDreams__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/FutureDreams */ \"(app-pages-browser)/./src/components/FutureDreams.tsx\");\n/* harmony import */ var _components_FloatingNavigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/FloatingNavigation */ \"(app-pages-browser)/./src/components/FloatingNavigation.tsx\");\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/LoadingScreen */ \"(app-pages-browser)/./src/components/LoadingScreen.tsx\");\n/* harmony import */ var _components_AudioManager__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/AudioManager */ \"(app-pages-browser)/./src/components/AudioManager.tsx\");\n/* harmony import */ var _components_CustomCursor__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/CustomCursor */ \"(app-pages-browser)/./src/components/CustomCursor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const handleLoadingComplete = ()=>{\n        setIsLoading(false);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            onLoadingComplete: handleLoadingComplete\n        }, void 0, false, {\n            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"overflow-x-hidden cursor-none\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LandingPage__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CelebrationRoom__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoveStoryTimeline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoveLetter__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SpinTheWheel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PhotoGallery__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VirtualHug__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FutureDreams__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FloatingNavigation__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioManager__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomCursor__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"5S7VQ8+9ArWv2AFPIfnY+LwrHeg=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});