"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/LandingPage.tsx":
/*!****************************************!*\
  !*** ./src/components/LandingPage.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst LandingPage = ()=>{\n    _s();\n    const [currentText, setCurrentText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showCursor, setShowCursor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasInteracted, setHasInteracted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMainContent, setShowMainContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start start\",\n            \"end start\"\n        ]\n    });\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        -200\n    ]);\n    const opacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        0.5\n    ], [\n        1,\n        0\n    ]);\n    const messages = [\n        \"Hi Roshni... 💖\",\n        \"Happy Birthday, My Love... 🎂\",\n        \"I made this just for you.\",\n        \"A world where only our love exists...\",\n        \"Click anywhere and step into the magic.\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (currentIndex < messages.length) {\n                const message = messages[currentIndex];\n                let charIndex = 0;\n                const typeInterval = setInterval({\n                    \"LandingPage.useEffect.typeInterval\": ()=>{\n                        if (charIndex <= message.length) {\n                            setCurrentText(message.slice(0, charIndex));\n                            charIndex++;\n                        } else {\n                            clearInterval(typeInterval);\n                            setTimeout({\n                                \"LandingPage.useEffect.typeInterval\": ()=>{\n                                    if (currentIndex < messages.length - 1) {\n                                        setCurrentIndex(currentIndex + 1);\n                                        setCurrentText('');\n                                    } else {\n                                        setShowMainContent(true);\n                                    }\n                                }\n                            }[\"LandingPage.useEffect.typeInterval\"], 2000);\n                        }\n                    }\n                }[\"LandingPage.useEffect.typeInterval\"], 80);\n                return ({\n                    \"LandingPage.useEffect\": ()=>clearInterval(typeInterval)\n                })[\"LandingPage.useEffect\"];\n            }\n        }\n    }[\"LandingPage.useEffect\"], [\n        currentIndex\n    ]);\n    const handleInteraction = ()=>{\n        if (!hasInteracted) {\n            setHasInteracted(true);\n        // Trigger audio or other effects here\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            const cursorInterval = setInterval({\n                \"LandingPage.useEffect.cursorInterval\": ()=>{\n                    setShowCursor({\n                        \"LandingPage.useEffect.cursorInterval\": (prev)=>!prev\n                    }[\"LandingPage.useEffect.cursorInterval\"]);\n                }\n            }[\"LandingPage.useEffect.cursorInterval\"], 500);\n            return ({\n                \"LandingPage.useEffect\": ()=>clearInterval(cursorInterval)\n            })[\"LandingPage.useEffect\"];\n        }\n    }[\"LandingPage.useEffect\"], []);\n    const scrollToNext = ()=>{\n        const nextSection = document.getElementById('celebration');\n        if (nextSection) {\n            nextSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        id: \"landing\",\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        onClick: handleInteraction,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-radial from-pink-500/20 via-purple-500/10 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            ...Array(100)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"absolute w-1 h-1 bg-white rounded-full\",\n                                style: {\n                                    left: \"\".concat((i * 17 + 23) % 100, \"%\"),\n                                    top: \"\".concat((i * 23 + 17) % 100, \"%\")\n                                },\n                                animate: {\n                                    opacity: [\n                                        0.3,\n                                        1,\n                                        0.3\n                                    ],\n                                    scale: [\n                                        0.5,\n                                        1.2,\n                                        0.5\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2 + i % 3,\n                                    repeat: Infinity,\n                                    delay: i * 0.05\n                                }\n                            }, \"star-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children: [\n                            ...Array(15)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"absolute\",\n                                style: {\n                                    left: \"\".concat((i * 13 + 31) % 100, \"%\"),\n                                    top: \"\".concat((i * 19 + 41) % 100, \"%\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -100,\n                                        -200\n                                    ],\n                                    x: [\n                                        0,\n                                        i % 2 === 0 ? 50 : -50,\n                                        0\n                                    ],\n                                    rotate: [\n                                        0,\n                                        360,\n                                        720\n                                    ],\n                                    opacity: [\n                                        0,\n                                        0.6,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 8 + i % 4,\n                                    repeat: Infinity,\n                                    delay: i * 0.8,\n                                    ease: \"easeInOut\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-6 rounded-full blur-sm\",\n                                    style: {\n                                        background: \"linear-gradient(45deg, #ff6b9d, #f368e0)\",\n                                        transform: \"rotate(\".concat(i * 45, \"deg)\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"petal-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children: [\n                            ...Array(12)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"absolute text-pink-400\",\n                                style: {\n                                    left: \"\".concat((i * 29 + 37) % 100, \"%\"),\n                                    top: \"\".concat((i * 31 + 43) % 100, \"%\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -60,\n                                        0\n                                    ],\n                                    scale: [\n                                        0.5,\n                                        1.5,\n                                        0.5\n                                    ],\n                                    opacity: [\n                                        0.2,\n                                        0.8,\n                                        0.2\n                                    ]\n                                },\n                                transition: {\n                                    duration: 6 + i % 3,\n                                    repeat: Infinity,\n                                    delay: i * 0.5\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    size: 20 + i % 3 * 10,\n                                    fill: \"currentColor\",\n                                    className: \"drop-shadow-lg filter blur-[0.5px]\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"glow-heart-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children: [\n                            ...Array(20)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"absolute text-yellow-300\",\n                                style: {\n                                    left: \"\".concat((i * 11 + 47) % 100, \"%\"),\n                                    top: \"\".concat((i * 13 + 53) % 100, \"%\")\n                                },\n                                animate: {\n                                    scale: [\n                                        0,\n                                        1,\n                                        0\n                                    ],\n                                    rotate: [\n                                        0,\n                                        180,\n                                        360\n                                    ],\n                                    opacity: [\n                                        0,\n                                        1,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3,\n                                    repeat: Infinity,\n                                    delay: i * 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 8 + i % 2 * 4\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, \"sparkle-\".concat(i), false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center z-10 px-4 max-w-5xl mx-auto relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-radial from-pink-200/20 via-purple-200/10 to-transparent rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 1.2,\n                            ease: \"easeOut\"\n                        },\n                        className: \"mb-12 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                                className: \"text-7xl md:text-9xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-pink-500 via-purple-500 to-rose-500 mb-6 leading-tight\",\n                                animate: {\n                                    backgroundPosition: [\n                                        '0% 50%',\n                                        '100% 50%',\n                                        '0% 50%'\n                                    ]\n                                },\n                                transition: {\n                                    duration: 8,\n                                    repeat: Infinity,\n                                    ease: \"linear\"\n                                },\n                                style: {\n                                    backgroundSize: '200% 200%'\n                                },\n                                children: \"Happy Birthday\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    scale: 0.8,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    scale: 1,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 0.5,\n                                    duration: 0.8\n                                },\n                                className: \"relative mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-rose-600 to-pink-600 mb-2\",\n                                        children: \"Roshni Jwala\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        animate: {\n                                            scale: [\n                                                1,\n                                                1.05,\n                                                1\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            repeat: Infinity\n                                        },\n                                        className: \"text-xl md:text-2xl text-gray-600 font-medium\",\n                                        children: \"My Beautiful Babu ✨\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 1,\n                                    duration: 0.8\n                                },\n                                className: \"text-lg md:text-xl text-gray-500 max-w-2xl mx-auto leading-relaxed\",\n                                children: \"This is your magical space. Click. Explore. Feel loved. \\uD83D\\uDC95\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-[120px] flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                delay: 1.5\n                            },\n                            className: \"text-xl md:text-2xl text-gray-600 font-medium\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-block\",\n                                children: [\n                                    currentText,\n                                    showCursor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block w-0.5 h-6 bg-pink-500 ml-1 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    currentIndex >= messages.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 2\n                        },\n                        className: \"mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: scrollToNext,\n                            className: \"group flex flex-col items-center text-pink-500 hover:text-pink-600 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"Scroll Down\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            10,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        size: 32,\n                                        className: \"group-hover:scale-110 transition-transform\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: 3\n                },\n                className: \"absolute bottom-8 right-8 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"bg-white/20 backdrop-blur-sm rounded-full p-3 text-pink-500 hover:bg-white/30 transition-all\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        size: 24,\n                        className: \"animate-heartbeat\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LandingPage, \"LyT8IY2gNQGSe7Wtskh1kocH5aw=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform\n    ];\n});\n_c = LandingPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LandingPage);\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LandingPage.tsx\n"));

/***/ })

});