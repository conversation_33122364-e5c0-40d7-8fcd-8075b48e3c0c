"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_LandingPage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LandingPage */ \"(app-pages-browser)/./src/components/LandingPage.tsx\");\n/* harmony import */ var _components_CelebrationRoom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CelebrationRoom */ \"(app-pages-browser)/./src/components/CelebrationRoom.tsx\");\n/* harmony import */ var _components_LoveStoryTimeline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/LoveStoryTimeline */ \"(app-pages-browser)/./src/components/LoveStoryTimeline.tsx\");\n/* harmony import */ var _components_LoveLetter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/LoveLetter */ \"(app-pages-browser)/./src/components/LoveLetter.tsx\");\n/* harmony import */ var _components_SpinTheWheel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/SpinTheWheel */ \"(app-pages-browser)/./src/components/SpinTheWheel.tsx\");\n/* harmony import */ var _components_PhotoGallery__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/PhotoGallery */ \"(app-pages-browser)/./src/components/PhotoGallery.tsx\");\n/* harmony import */ var _components_VirtualHug__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/VirtualHug */ \"(app-pages-browser)/./src/components/VirtualHug.tsx\");\n/* harmony import */ var _components_FutureDreams__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/FutureDreams */ \"(app-pages-browser)/./src/components/FutureDreams.tsx\");\n/* harmony import */ var _components_FloatingNavigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/FloatingNavigation */ \"(app-pages-browser)/./src/components/FloatingNavigation.tsx\");\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/LoadingScreen */ \"(app-pages-browser)/./src/components/LoadingScreen.tsx\");\n/* harmony import */ var _components_AudioManager__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/AudioManager */ \"(app-pages-browser)/./src/components/AudioManager.tsx\");\n/* harmony import */ var _components_CustomCursor__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/CustomCursor */ \"(app-pages-browser)/./src/components/CustomCursor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const handleLoadingComplete = ()=>{\n        setIsLoading(false);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            onLoadingComplete: handleLoadingComplete\n        }, void 0, false, {\n            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 25,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"overflow-x-hidden cursor-none\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LandingPage__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CelebrationRoom__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoveStoryTimeline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoveLetter__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SpinTheWheel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PhotoGallery__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VirtualHug__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FutureDreams__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FloatingNavigation__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioManager__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomCursor__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"5S7VQ8+9ArWv2AFPIfnY+LwrHeg=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/CustomCursor.tsx":
/*!*****************************************!*\
  !*** ./src/components/CustomCursor.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst CustomCursor = ()=>{\n    _s();\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [isHovering, setIsHovering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CustomCursor.useEffect\": ()=>{\n            const updateMousePosition = {\n                \"CustomCursor.useEffect.updateMousePosition\": (e)=>{\n                    setMousePosition({\n                        x: e.clientX,\n                        y: e.clientY\n                    });\n                }\n            }[\"CustomCursor.useEffect.updateMousePosition\"];\n            const handleMouseEnter = {\n                \"CustomCursor.useEffect.handleMouseEnter\": ()=>setIsHovering(true)\n            }[\"CustomCursor.useEffect.handleMouseEnter\"];\n            const handleMouseLeave = {\n                \"CustomCursor.useEffect.handleMouseLeave\": ()=>setIsHovering(false)\n            }[\"CustomCursor.useEffect.handleMouseLeave\"];\n            // Add event listeners for mouse movement\n            window.addEventListener('mousemove', updateMousePosition);\n            // Add event listeners for interactive elements\n            const interactiveElements = document.querySelectorAll('button, a, [role=\"button\"]');\n            interactiveElements.forEach({\n                \"CustomCursor.useEffect\": (el)=>{\n                    el.addEventListener('mouseenter', handleMouseEnter);\n                    el.addEventListener('mouseleave', handleMouseLeave);\n                }\n            }[\"CustomCursor.useEffect\"]);\n            return ({\n                \"CustomCursor.useEffect\": ()=>{\n                    window.removeEventListener('mousemove', updateMousePosition);\n                    interactiveElements.forEach({\n                        \"CustomCursor.useEffect\": (el)=>{\n                            el.removeEventListener('mouseenter', handleMouseEnter);\n                            el.removeEventListener('mouseleave', handleMouseLeave);\n                        }\n                    }[\"CustomCursor.useEffect\"]);\n                }\n            })[\"CustomCursor.useEffect\"];\n        }\n    }[\"CustomCursor.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"fixed top-0 left-0 pointer-events-none z-[9999] mix-blend-difference\",\n                animate: {\n                    x: mousePosition.x - 10,\n                    y: mousePosition.y - 10,\n                    scale: isHovering ? 1.5 : 1\n                },\n                transition: {\n                    type: \"spring\",\n                    stiffness: 500,\n                    damping: 28\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-5 h-5 bg-pink-500 rounded-full\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\CustomCursor.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\CustomCursor.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"fixed top-0 left-0 pointer-events-none z-[9998]\",\n                animate: {\n                    x: mousePosition.x - 20,\n                    y: mousePosition.y - 20,\n                    scale: isHovering ? 2 : 1\n                },\n                transition: {\n                    type: \"spring\",\n                    stiffness: 150,\n                    damping: 15\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 border-2 border-pink-300 rounded-full opacity-50\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\CustomCursor.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\CustomCursor.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CustomCursor, \"80qGfRMDuQTxUjYNjmdC3+mifCc=\");\n_c = CustomCursor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomCursor);\nvar _c;\n$RefreshReg$(_c, \"CustomCursor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CustomCursor.tsx\n"));

/***/ })

});