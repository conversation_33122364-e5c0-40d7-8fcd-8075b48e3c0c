"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/VirtualHug.tsx":
/*!***************************************!*\
  !*** ./src/components/VirtualHug.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Sparkles,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Sparkles,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Sparkles,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Sparkles,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst VirtualHug = ()=>{\n    _s();\n    const [isHugging, setIsHugging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHeartbeat, setShowHeartbeat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [audioEnabled, setAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hugCount, setHugCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const startHug = ()=>{\n        setIsHugging(true);\n        setShowHeartbeat(true);\n        setHugCount((prev)=>prev + 1);\n        // Simulate heartbeat sound effect\n        if (audioEnabled) {\n            // In a real implementation, you would play actual audio here\n            console.log('Playing heartbeat sound...');\n        }\n        // Reset after 5 seconds\n        setTimeout(()=>{\n            setIsHugging(false);\n            setShowHeartbeat(false);\n        }, 5000);\n    };\n    const toggleAudio = ()=>{\n        setAudioEnabled(!audioEnabled);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"hug\",\n        className: \"min-h-screen bg-gradient-to-br from-rose-100 via-pink-100 to-purple-100 flex items-center justify-center py-16 px-4 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    ...Array(20)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute text-pink-300 opacity-20\",\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\")\n                        },\n                        animate: {\n                            y: [\n                                0,\n                                -50,\n                                0\n                            ],\n                            rotate: [\n                                0,\n                                360\n                            ],\n                            scale: [\n                                0.5,\n                                1.5,\n                                0.5\n                            ]\n                        },\n                        transition: {\n                            duration: 4 + Math.random() * 3,\n                            repeat: Infinity,\n                            delay: Math.random() * 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            size: 15 + Math.random() * 25,\n                            fill: \"currentColor\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, undefined)\n                    }, i, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto text-center relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h2, {\n                        initial: {\n                            opacity: 0,\n                            y: -50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-rose-600 to-pink-600 mb-8\",\n                        children: \"Missing Me?\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                        initial: {\n                            opacity: 0\n                        },\n                        whileInView: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.3\n                        },\n                        className: \"text-xl text-gray-600 mb-12 max-w-2xl mx-auto\",\n                        children: \"Whenever you miss me, just click the button below for a warm virtual hug from your Saurabh \\uD83E\\uDD17\\uD83D\\uDC95\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                        onClick: toggleAudio,\n                        className: \"absolute top-0 right-0 bg-white/20 backdrop-blur-sm rounded-full p-3 text-pink-500 hover:bg-white/30 transition-all mb-8\",\n                        children: audioEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 27\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 61\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.8\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-lg\",\n                            children: [\n                                \"Virtual hugs given: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-pink-600 font-bold\",\n                                    children: hugCount\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 33\n                                }, undefined),\n                                \" \\uD83E\\uDD17\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"relative mb-12\",\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        whileTap: {\n                            scale: 0.95\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                            onClick: startHug,\n                            disabled: isHugging,\n                            className: \"\\n              relative w-64 h-64 rounded-full text-white font-bold text-xl shadow-2xl transition-all duration-500\\n              \".concat(isHugging ? 'bg-gradient-to-r from-red-400 to-pink-400 cursor-not-allowed' : 'bg-gradient-to-r from-rose-500 to-pink-500 hover:from-rose-600 hover:to-pink-600', \"\\n            \"),\n                            animate: isHugging ? {\n                                scale: [\n                                    1,\n                                    1.1,\n                                    1\n                                ],\n                                boxShadow: [\n                                    \"0 0 0 0 rgba(236, 72, 153, 0.7)\",\n                                    \"0 0 0 20px rgba(236, 72, 153, 0)\",\n                                    \"0 0 0 0 rgba(236, 72, 153, 0)\"\n                                ]\n                            } : {},\n                            transition: {\n                                duration: 1,\n                                repeat: isHugging ? Infinity : 0\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-16 h-16 mb-4 \".concat(isHugging ? 'animate-pulse' : ''),\n                                            fill: \"currentColor\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg\",\n                                            children: isHugging ? 'Hugging...' : 'Click if you miss me'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, undefined),\n                                isHugging && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"absolute inset-0 rounded-full border-4 border-white\",\n                                    animate: {\n                                        scale: [\n                                            1,\n                                            2,\n                                            3\n                                        ],\n                                        opacity: [\n                                            1,\n                                            0.5,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        children: isHugging && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -50\n                            },\n                            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl max-w-2xl mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                animate: {\n                                    scale: [\n                                        1,\n                                        1.05,\n                                        1\n                                    ]\n                                },\n                                transition: {\n                                    duration: 1,\n                                    repeat: Infinity\n                                },\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-12 h-12 text-pink-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-dancing-script text-gray-800 mb-4\",\n                                        children: \"Here's a tight virtual hug, baby... \\uD83E\\uDD17\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 leading-relaxed mb-4\",\n                                        children: \"I'm wrapping my arms around you right now, holding you close to my heart. Feel my love surrounding you, keeping you warm and safe. You're never alone, Roshni - I'm always here with you, loving you endlessly.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center space-x-2\",\n                                        children: [\n                                            ...Array(7)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4 text-pink-400 animate-pulse\",\n                                                fill: \"currentColor\",\n                                                style: {\n                                                    animationDelay: \"\".concat(i * 0.2, \"s\")\n                                                }\n                                            }, i, false, {\n                                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        children: showHeartbeat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            exit: {\n                                opacity: 0\n                            },\n                            className: \"fixed inset-0 pointer-events-none z-0\",\n                            children: [\n                                ...Array(30)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"absolute w-2 h-2 bg-pink-400 rounded-full\",\n                                    style: {\n                                        left: \"\".concat(Math.random() * 100, \"%\"),\n                                        top: \"\".concat(Math.random() * 100, \"%\")\n                                    },\n                                    animate: {\n                                        scale: [\n                                            0,\n                                            1,\n                                            0\n                                        ],\n                                        opacity: [\n                                            0,\n                                            1,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 1,\n                                        repeat: Infinity,\n                                        delay: Math.random() * 2\n                                    }\n                                }, i, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        whileInView: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.5\n                        },\n                        className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            {\n                                title: \"Always Here\",\n                                message: \"No matter how far apart we are, my love reaches you instantly ❤️\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-8 h-8\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 21\n                                }, undefined)\n                            },\n                            {\n                                title: \"Thinking of You\",\n                                message: \"Every moment of every day, you're in my thoughts and in my heart 💭\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-8 h-8\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 21\n                                }, undefined)\n                            },\n                            {\n                                title: \"Forever Yours\",\n                                message: \"Distance means nothing when someone means everything to you 💕\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-8 h-8\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 21\n                                }, undefined)\n                            }\n                        ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.7 + index * 0.2\n                                },\n                                className: \"bg-white/60 backdrop-blur-sm rounded-xl p-6 text-center hover:bg-white/80 transition-all\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-pink-500 mb-4 flex justify-center\",\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-dancing-script text-xl text-gray-800 mb-2\",\n                                        children: item.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm leading-relaxed\",\n                                        children: item.message\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VirtualHug, \"jqx2JVD62gID9DWojKVdQur969M=\");\n_c = VirtualHug;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VirtualHug);\nvar _c;\n$RefreshReg$(_c, \"VirtualHug\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1ZpcnR1YWxIdWcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNZO0FBQ1M7QUFFakUsTUFBTU8sYUFBYTs7SUFDakIsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdULCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ1UsZUFBZUMsaUJBQWlCLEdBQUdYLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ1ksY0FBY0MsZ0JBQWdCLEdBQUdiLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ2MsVUFBVUMsWUFBWSxHQUFHZiwrQ0FBUUEsQ0FBQztJQUV6QyxNQUFNZ0IsV0FBVztRQUNmUCxhQUFhO1FBQ2JFLGlCQUFpQjtRQUNqQkksWUFBWUUsQ0FBQUEsT0FBUUEsT0FBTztRQUUzQixrQ0FBa0M7UUFDbEMsSUFBSUwsY0FBYztZQUNoQiw2REFBNkQ7WUFDN0RNLFFBQVFDLEdBQUcsQ0FBQztRQUNkO1FBRUEsd0JBQXdCO1FBQ3hCQyxXQUFXO1lBQ1RYLGFBQWE7WUFDYkUsaUJBQWlCO1FBQ25CLEdBQUc7SUFDTDtJQUVBLE1BQU1VLGNBQWM7UUFDbEJSLGdCQUFnQixDQUFDRDtJQUNuQjtJQUVBLHFCQUNFLDhEQUFDVTtRQUFJQyxJQUFHO1FBQU1DLFdBQVU7OzBCQUV0Qiw4REFBQ0Y7Z0JBQUlFLFdBQVU7MEJBQ1o7dUJBQUlDLE1BQU07aUJBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLEdBQUdDLGtCQUN0Qiw4REFBQzNCLGlEQUFNQSxDQUFDcUIsR0FBRzt3QkFFVEUsV0FBVTt3QkFDVkssT0FBTzs0QkFDTEMsTUFBTSxHQUF1QixPQUFwQkMsS0FBS0MsTUFBTSxLQUFLLEtBQUk7NEJBQzdCQyxLQUFLLEdBQXVCLE9BQXBCRixLQUFLQyxNQUFNLEtBQUssS0FBSTt3QkFDOUI7d0JBQ0FFLFNBQVM7NEJBQ1BDLEdBQUc7Z0NBQUM7Z0NBQUcsQ0FBQztnQ0FBSTs2QkFBRTs0QkFDZEMsUUFBUTtnQ0FBQztnQ0FBRzs2QkFBSTs0QkFDaEJDLE9BQU87Z0NBQUM7Z0NBQUs7Z0NBQUs7NkJBQUk7d0JBQ3hCO3dCQUNBQyxZQUFZOzRCQUNWQyxVQUFVLElBQUlSLEtBQUtDLE1BQU0sS0FBSzs0QkFDOUJRLFFBQVFDOzRCQUNSQyxPQUFPWCxLQUFLQyxNQUFNLEtBQUs7d0JBQ3pCO2tDQUVBLDRFQUFDN0IsMEdBQUtBOzRCQUFDd0MsTUFBTSxLQUFLWixLQUFLQyxNQUFNLEtBQUs7NEJBQUlZLE1BQUs7Ozs7Ozt1QkFqQnRDaEI7Ozs7Ozs7Ozs7MEJBc0JYLDhEQUFDTjtnQkFBSUUsV0FBVTs7a0NBQ2IsOERBQUN2QixpREFBTUEsQ0FBQzRDLEVBQUU7d0JBQ1JDLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUdaLEdBQUcsQ0FBQzt3QkFBRzt3QkFDOUJhLGFBQWE7NEJBQUVELFNBQVM7NEJBQUdaLEdBQUc7d0JBQUU7d0JBQ2hDRyxZQUFZOzRCQUFFQyxVQUFVO3dCQUFJO3dCQUM1QmYsV0FBVTtrQ0FDWDs7Ozs7O2tDQUlELDhEQUFDdkIsaURBQU1BLENBQUNnRCxDQUFDO3dCQUNQSCxTQUFTOzRCQUFFQyxTQUFTO3dCQUFFO3dCQUN0QkMsYUFBYTs0QkFBRUQsU0FBUzt3QkFBRTt3QkFDMUJULFlBQVk7NEJBQUVJLE9BQU87d0JBQUk7d0JBQ3pCbEIsV0FBVTtrQ0FDWDs7Ozs7O2tDQUtELDhEQUFDdkIsaURBQU1BLENBQUNpRCxNQUFNO3dCQUNaQyxTQUFTOUI7d0JBQ1RHLFdBQVU7a0NBRVRaLDZCQUFlLDhEQUFDUCwwR0FBT0E7NEJBQUNtQixXQUFVOzs7OztzREFBZSw4REFBQ2xCLDBHQUFPQTs0QkFBQ2tCLFdBQVU7Ozs7Ozs7Ozs7O2tDQUl2RSw4REFBQ3ZCLGlEQUFNQSxDQUFDcUIsR0FBRzt3QkFDVHdCLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUdWLE9BQU87d0JBQUk7d0JBQ2xDVyxhQUFhOzRCQUFFRCxTQUFTOzRCQUFHVixPQUFPO3dCQUFFO3dCQUNwQ2IsV0FBVTtrQ0FFViw0RUFBQ3lCOzRCQUFFekIsV0FBVTs7Z0NBQXdCOzhDQUNmLDhEQUFDNEI7b0NBQUs1QixXQUFVOzhDQUEyQlY7Ozs7OztnQ0FBZ0I7Ozs7Ozs7Ozs7OztrQ0FLbkYsOERBQUNiLGlEQUFNQSxDQUFDcUIsR0FBRzt3QkFDVEUsV0FBVTt3QkFDVjZCLFlBQVk7NEJBQUVoQixPQUFPO3dCQUFLO3dCQUMxQmlCLFVBQVU7NEJBQUVqQixPQUFPO3dCQUFLO2tDQUV4Qiw0RUFBQ3BDLGlEQUFNQSxDQUFDaUQsTUFBTTs0QkFDWkMsU0FBU25DOzRCQUNUdUMsVUFBVS9DOzRCQUNWZ0IsV0FBVyxzSUFLUixPQUhDaEIsWUFDRSxpRUFDQSxvRkFDSDs0QkFFSDBCLFNBQVMxQixZQUFZO2dDQUNuQjZCLE9BQU87b0NBQUM7b0NBQUc7b0NBQUs7aUNBQUU7Z0NBQ2xCbUIsV0FBVztvQ0FDVDtvQ0FDQTtvQ0FDQTtpQ0FDRDs0QkFDSCxJQUFJLENBQUM7NEJBQ0xsQixZQUFZO2dDQUNWQyxVQUFVO2dDQUNWQyxRQUFRaEMsWUFBWWlDLFdBQVc7NEJBQ2pDOzs4Q0FFQSw4REFBQ25CO29DQUFJRSxXQUFVOztzREFDYiw4REFBQ3JCLDBHQUFLQTs0Q0FDSnFCLFdBQVcsa0JBQW1ELE9BQWpDaEIsWUFBWSxrQkFBa0I7NENBQzNEb0MsTUFBSzs7Ozs7O3NEQUVQLDhEQUFDUTs0Q0FBSzVCLFdBQVU7c0RBQ2JoQixZQUFZLGVBQWU7Ozs7Ozs7Ozs7OztnQ0FLL0JBLDJCQUNDLDhEQUFDUCxpREFBTUEsQ0FBQ3FCLEdBQUc7b0NBQ1RFLFdBQVU7b0NBQ1ZVLFNBQVM7d0NBQ1BHLE9BQU87NENBQUM7NENBQUc7NENBQUc7eUNBQUU7d0NBQ2hCVSxTQUFTOzRDQUFDOzRDQUFHOzRDQUFLO3lDQUFFO29DQUN0QjtvQ0FDQVQsWUFBWTt3Q0FDVkMsVUFBVTt3Q0FDVkMsUUFBUUM7b0NBQ1Y7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9SLDhEQUFDdkMsMERBQWVBO2tDQUNiTSwyQkFDQyw4REFBQ1AsaURBQU1BLENBQUNxQixHQUFHOzRCQUNUd0IsU0FBUztnQ0FBRUMsU0FBUztnQ0FBR1osR0FBRzs0QkFBRzs0QkFDN0JELFNBQVM7Z0NBQUVhLFNBQVM7Z0NBQUdaLEdBQUc7NEJBQUU7NEJBQzVCc0IsTUFBTTtnQ0FBRVYsU0FBUztnQ0FBR1osR0FBRyxDQUFDOzRCQUFHOzRCQUMzQlgsV0FBVTtzQ0FFViw0RUFBQ3ZCLGlEQUFNQSxDQUFDcUIsR0FBRztnQ0FDVFksU0FBUztvQ0FBRUcsT0FBTzt3Q0FBQzt3Q0FBRzt3Q0FBTTtxQ0FBRTtnQ0FBQztnQ0FDL0JDLFlBQVk7b0NBQUVDLFVBQVU7b0NBQUdDLFFBQVFDO2dDQUFTO2dDQUM1Q2pCLFdBQVU7O2tEQUVWLDhEQUFDcEIsMEdBQVFBO3dDQUFDb0IsV0FBVTs7Ozs7O2tEQUNwQiw4REFBQ2tDO3dDQUFHbEMsV0FBVTtrREFBa0Q7Ozs7OztrREFHaEUsOERBQUN5Qjt3Q0FBRXpCLFdBQVU7a0RBQXFDOzs7Ozs7a0RBS2xELDhEQUFDRjt3Q0FBSUUsV0FBVTtrREFDWjsrQ0FBSUMsTUFBTTt5Q0FBRyxDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsR0FBR0Msa0JBQ3JCLDhEQUFDekIsMEdBQUtBO2dEQUVKcUIsV0FBVTtnREFDVm9CLE1BQUs7Z0RBQ0xmLE9BQU87b0RBQUU4QixnQkFBZ0IsR0FBVyxPQUFSL0IsSUFBSSxLQUFJO2dEQUFHOytDQUhsQ0E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQWFuQiw4REFBQzFCLDBEQUFlQTtrQ0FDYlEsK0JBQ0MsOERBQUNULGlEQUFNQSxDQUFDcUIsR0FBRzs0QkFDVHdCLFNBQVM7Z0NBQUVDLFNBQVM7NEJBQUU7NEJBQ3RCYixTQUFTO2dDQUFFYSxTQUFTOzRCQUFFOzRCQUN0QlUsTUFBTTtnQ0FBRVYsU0FBUzs0QkFBRTs0QkFDbkJ2QixXQUFVO3NDQUdUO21DQUFJQyxNQUFNOzZCQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDQyxHQUFHQyxrQkFDdEIsOERBQUMzQixpREFBTUEsQ0FBQ3FCLEdBQUc7b0NBRVRFLFdBQVU7b0NBQ1ZLLE9BQU87d0NBQ0xDLE1BQU0sR0FBdUIsT0FBcEJDLEtBQUtDLE1BQU0sS0FBSyxLQUFJO3dDQUM3QkMsS0FBSyxHQUF1QixPQUFwQkYsS0FBS0MsTUFBTSxLQUFLLEtBQUk7b0NBQzlCO29DQUNBRSxTQUFTO3dDQUNQRyxPQUFPOzRDQUFDOzRDQUFHOzRDQUFHO3lDQUFFO3dDQUNoQlUsU0FBUzs0Q0FBQzs0Q0FBRzs0Q0FBRzt5Q0FBRTtvQ0FDcEI7b0NBQ0FULFlBQVk7d0NBQ1ZDLFVBQVU7d0NBQ1ZDLFFBQVFDO3dDQUNSQyxPQUFPWCxLQUFLQyxNQUFNLEtBQUs7b0NBQ3pCO21DQWRLSjs7Ozs7Ozs7Ozs7Ozs7O2tDQXNCZiw4REFBQzNCLGlEQUFNQSxDQUFDcUIsR0FBRzt3QkFDVHdCLFNBQVM7NEJBQUVDLFNBQVM7d0JBQUU7d0JBQ3RCQyxhQUFhOzRCQUFFRCxTQUFTO3dCQUFFO3dCQUMxQlQsWUFBWTs0QkFBRUksT0FBTzt3QkFBSTt3QkFDekJsQixXQUFVO2tDQUVUOzRCQUNDO2dDQUNFb0MsT0FBTztnQ0FDUEMsU0FBUztnQ0FDVEMsb0JBQU0sOERBQUMzRCwwR0FBS0E7b0NBQUNxQixXQUFVOzs7Ozs7NEJBQ3pCOzRCQUNBO2dDQUNFb0MsT0FBTztnQ0FDUEMsU0FBUztnQ0FDVEMsb0JBQU0sOERBQUMxRCwwR0FBUUE7b0NBQUNvQixXQUFVOzs7Ozs7NEJBQzVCOzRCQUNBO2dDQUNFb0MsT0FBTztnQ0FDUEMsU0FBUztnQ0FDVEMsb0JBQU0sOERBQUMzRCwwR0FBS0E7b0NBQUNxQixXQUFVOzs7Ozs7NEJBQ3pCO3lCQUNELENBQUNFLEdBQUcsQ0FBQyxDQUFDcUMsTUFBTUMsc0JBQ1gsOERBQUMvRCxpREFBTUEsQ0FBQ3FCLEdBQUc7Z0NBRVR3QixTQUFTO29DQUFFQyxTQUFTO29DQUFHWixHQUFHO2dDQUFHO2dDQUM3QmEsYUFBYTtvQ0FBRUQsU0FBUztvQ0FBR1osR0FBRztnQ0FBRTtnQ0FDaENHLFlBQVk7b0NBQUVJLE9BQU8sTUFBTXNCLFFBQVE7Z0NBQUk7Z0NBQ3ZDeEMsV0FBVTs7a0RBRVYsOERBQUNGO3dDQUFJRSxXQUFVO2tEQUNadUMsS0FBS0QsSUFBSTs7Ozs7O2tEQUVaLDhEQUFDRzt3Q0FBR3pDLFdBQVU7a0RBQ1h1QyxLQUFLSCxLQUFLOzs7Ozs7a0RBRWIsOERBQUNYO3dDQUFFekIsV0FBVTtrREFDVnVDLEtBQUtGLE9BQU87Ozs7Ozs7K0JBYlZHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBcUJuQjtHQTNRTXpEO0tBQUFBO0FBNlFOLGlFQUFlQSxVQUFVQSxFQUFDIiwic291cmNlcyI6WyJEOlxcUk9TSE5JXFxmb3JSb3NobmlcXGZvci1yb3NobmlcXHNyY1xcY29tcG9uZW50c1xcVmlydHVhbEh1Zy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tICdmcmFtZXItbW90aW9uJztcbmltcG9ydCB7IEhlYXJ0LCBTcGFya2xlcywgVm9sdW1lMiwgVm9sdW1lWCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5cbmNvbnN0IFZpcnR1YWxIdWcgPSAoKSA9PiB7XG4gIGNvbnN0IFtpc0h1Z2dpbmcsIHNldElzSHVnZ2luZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93SGVhcnRiZWF0LCBzZXRTaG93SGVhcnRiZWF0XSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2F1ZGlvRW5hYmxlZCwgc2V0QXVkaW9FbmFibGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2h1Z0NvdW50LCBzZXRIdWdDb3VudF0gPSB1c2VTdGF0ZSgwKTtcblxuICBjb25zdCBzdGFydEh1ZyA9ICgpID0+IHtcbiAgICBzZXRJc0h1Z2dpbmcodHJ1ZSk7XG4gICAgc2V0U2hvd0hlYXJ0YmVhdCh0cnVlKTtcbiAgICBzZXRIdWdDb3VudChwcmV2ID0+IHByZXYgKyAxKTtcbiAgICBcbiAgICAvLyBTaW11bGF0ZSBoZWFydGJlYXQgc291bmQgZWZmZWN0XG4gICAgaWYgKGF1ZGlvRW5hYmxlZCkge1xuICAgICAgLy8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB5b3Ugd291bGQgcGxheSBhY3R1YWwgYXVkaW8gaGVyZVxuICAgICAgY29uc29sZS5sb2coJ1BsYXlpbmcgaGVhcnRiZWF0IHNvdW5kLi4uJyk7XG4gICAgfVxuXG4gICAgLy8gUmVzZXQgYWZ0ZXIgNSBzZWNvbmRzXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBzZXRJc0h1Z2dpbmcoZmFsc2UpO1xuICAgICAgc2V0U2hvd0hlYXJ0YmVhdChmYWxzZSk7XG4gICAgfSwgNTAwMCk7XG4gIH07XG5cbiAgY29uc3QgdG9nZ2xlQXVkaW8gPSAoKSA9PiB7XG4gICAgc2V0QXVkaW9FbmFibGVkKCFhdWRpb0VuYWJsZWQpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBpZD1cImh1Z1wiIGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLXJvc2UtMTAwIHZpYS1waW5rLTEwMCB0by1wdXJwbGUtMTAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LTE2IHB4LTQgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICB7LyogQmFja2dyb3VuZCBIZWFydHMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgcG9pbnRlci1ldmVudHMtbm9uZVwiPlxuICAgICAgICB7Wy4uLkFycmF5KDIwKV0ubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGtleT17aX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRleHQtcGluay0zMDAgb3BhY2l0eS0yMFwiXG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICBsZWZ0OiBgJHtNYXRoLnJhbmRvbSgpICogMTAwfSVgLFxuICAgICAgICAgICAgICB0b3A6IGAke01hdGgucmFuZG9tKCkgKiAxMDB9JWAsXG4gICAgICAgICAgICB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17e1xuICAgICAgICAgICAgICB5OiBbMCwgLTUwLCAwXSxcbiAgICAgICAgICAgICAgcm90YXRlOiBbMCwgMzYwXSxcbiAgICAgICAgICAgICAgc2NhbGU6IFswLjUsIDEuNSwgMC41XSxcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7XG4gICAgICAgICAgICAgIGR1cmF0aW9uOiA0ICsgTWF0aC5yYW5kb20oKSAqIDMsXG4gICAgICAgICAgICAgIHJlcGVhdDogSW5maW5pdHksXG4gICAgICAgICAgICAgIGRlbGF5OiBNYXRoLnJhbmRvbSgpICogMyxcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEhlYXJ0IHNpemU9ezE1ICsgTWF0aC5yYW5kb20oKSAqIDI1fSBmaWxsPVwiY3VycmVudENvbG9yXCIgLz5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG8gdGV4dC1jZW50ZXIgcmVsYXRpdmUgei0xMFwiPlxuICAgICAgICA8bW90aW9uLmgyXG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAtNTAgfX1cbiAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44IH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC01eGwgbWQ6dGV4dC03eGwgZm9udC1kYW5jaW5nLXNjcmlwdCB0ZXh0LXRyYW5zcGFyZW50IGJnLWNsaXAtdGV4dCBiZy1ncmFkaWVudC10by1yIGZyb20tcm9zZS02MDAgdG8tcGluay02MDAgbWItOFwiXG4gICAgICAgID5cbiAgICAgICAgICBNaXNzaW5nIE1lP1xuICAgICAgICA8L21vdGlvbi5oMj5cblxuICAgICAgICA8bW90aW9uLnBcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMC4zIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWdyYXktNjAwIG1iLTEyIG1heC13LTJ4bCBteC1hdXRvXCJcbiAgICAgICAgPlxuICAgICAgICAgIFdoZW5ldmVyIHlvdSBtaXNzIG1lLCBqdXN0IGNsaWNrIHRoZSBidXR0b24gYmVsb3cgZm9yIGEgd2FybSB2aXJ0dWFsIGh1ZyBmcm9tIHlvdXIgU2F1cmFiaCDwn6SX8J+SlVxuICAgICAgICA8L21vdGlvbi5wPlxuXG4gICAgICAgIHsvKiBBdWRpbyBUb2dnbGUgKi99XG4gICAgICAgIDxtb3Rpb24uYnV0dG9uXG4gICAgICAgICAgb25DbGljaz17dG9nZ2xlQXVkaW99XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgcmlnaHQtMCBiZy13aGl0ZS8yMCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtZnVsbCBwLTMgdGV4dC1waW5rLTUwMCBob3ZlcjpiZy13aGl0ZS8zMCB0cmFuc2l0aW9uLWFsbCBtYi04XCJcbiAgICAgICAgPlxuICAgICAgICAgIHthdWRpb0VuYWJsZWQgPyA8Vm9sdW1lMiBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz4gOiA8Vm9sdW1lWCBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz59XG4gICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cblxuICAgICAgICB7LyogSHVnIENvdW50ZXIgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC44IH19XG4gICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgc2NhbGU6IDEgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJtYi04XCJcbiAgICAgICAgPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1sZ1wiPlxuICAgICAgICAgICAgVmlydHVhbCBodWdzIGdpdmVuOiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXBpbmstNjAwIGZvbnQtYm9sZFwiPntodWdDb3VudH08L3NwYW4+IPCfpJdcbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICB7LyogTWFpbiBIdWcgQnV0dG9uICovfVxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIG1iLTEyXCJcbiAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjA1IH19XG4gICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTUgfX1cbiAgICAgICAgPlxuICAgICAgICAgIDxtb3Rpb24uYnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtzdGFydEh1Z31cbiAgICAgICAgICAgIGRpc2FibGVkPXtpc0h1Z2dpbmd9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgcmVsYXRpdmUgdy02NCBoLTY0IHJvdW5kZWQtZnVsbCB0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LXhsIHNoYWRvdy0yeGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwXG4gICAgICAgICAgICAgICR7aXNIdWdnaW5nIFxuICAgICAgICAgICAgICAgID8gJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1yZWQtNDAwIHRvLXBpbmstNDAwIGN1cnNvci1ub3QtYWxsb3dlZCcgXG4gICAgICAgICAgICAgICAgOiAnYmctZ3JhZGllbnQtdG8tciBmcm9tLXJvc2UtNTAwIHRvLXBpbmstNTAwIGhvdmVyOmZyb20tcm9zZS02MDAgaG92ZXI6dG8tcGluay02MDAnXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGB9XG4gICAgICAgICAgICBhbmltYXRlPXtpc0h1Z2dpbmcgPyB7XG4gICAgICAgICAgICAgIHNjYWxlOiBbMSwgMS4xLCAxXSxcbiAgICAgICAgICAgICAgYm94U2hhZG93OiBbXG4gICAgICAgICAgICAgICAgXCIwIDAgMCAwIHJnYmEoMjM2LCA3MiwgMTUzLCAwLjcpXCIsXG4gICAgICAgICAgICAgICAgXCIwIDAgMCAyMHB4IHJnYmEoMjM2LCA3MiwgMTUzLCAwKVwiLFxuICAgICAgICAgICAgICAgIFwiMCAwIDAgMCByZ2JhKDIzNiwgNzIsIDE1MywgMClcIlxuICAgICAgICAgICAgICBdXG4gICAgICAgICAgICB9IDoge319XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7XG4gICAgICAgICAgICAgIGR1cmF0aW9uOiAxLFxuICAgICAgICAgICAgICByZXBlYXQ6IGlzSHVnZ2luZyA/IEluZmluaXR5IDogMCxcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLWZ1bGxcIj5cbiAgICAgICAgICAgICAgPEhlYXJ0IFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctMTYgaC0xNiBtYi00ICR7aXNIdWdnaW5nID8gJ2FuaW1hdGUtcHVsc2UnIDogJyd9YH0gXG4gICAgICAgICAgICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiIFxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnXCI+XG4gICAgICAgICAgICAgICAge2lzSHVnZ2luZyA/ICdIdWdnaW5nLi4uJyA6ICdDbGljayBpZiB5b3UgbWlzcyBtZSd9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogUmlwcGxlIEVmZmVjdCAqL31cbiAgICAgICAgICAgIHtpc0h1Z2dpbmcgJiYgKFxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgcm91bmRlZC1mdWxsIGJvcmRlci00IGJvcmRlci13aGl0ZVwiXG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17e1xuICAgICAgICAgICAgICAgICAgc2NhbGU6IFsxLCAyLCAzXSxcbiAgICAgICAgICAgICAgICAgIG9wYWNpdHk6IFsxLCAwLjUsIDBdLFxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17e1xuICAgICAgICAgICAgICAgICAgZHVyYXRpb246IDIsXG4gICAgICAgICAgICAgICAgICByZXBlYXQ6IEluZmluaXR5LFxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgIHsvKiBIdWcgTWVzc2FnZSAqL31cbiAgICAgICAgPEFuaW1hdGVQcmVzZW5jZT5cbiAgICAgICAgICB7aXNIdWdnaW5nICYmIChcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogNTAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgeTogLTUwIH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlLzgwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC0yeGwgcC04IHNoYWRvdy14bCBtYXgtdy0yeGwgbXgtYXV0b1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBzY2FsZTogWzEsIDEuMDUsIDFdIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMSwgcmVwZWF0OiBJbmZpbml0eSB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxTcGFya2xlcyBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgdGV4dC1waW5rLTUwMCBteC1hdXRvIG1iLTRcIiAvPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWRhbmNpbmctc2NyaXB0IHRleHQtZ3JheS04MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgSGVyZSdzIGEgdGlnaHQgdmlydHVhbCBodWcsIGJhYnkuLi4g8J+kl1xuICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBsZWFkaW5nLXJlbGF4ZWQgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgSSdtIHdyYXBwaW5nIG15IGFybXMgYXJvdW5kIHlvdSByaWdodCBub3csIGhvbGRpbmcgeW91IGNsb3NlIHRvIG15IGhlYXJ0LiBcbiAgICAgICAgICAgICAgICAgIEZlZWwgbXkgbG92ZSBzdXJyb3VuZGluZyB5b3UsIGtlZXBpbmcgeW91IHdhcm0gYW5kIHNhZmUuIFxuICAgICAgICAgICAgICAgICAgWW91J3JlIG5ldmVyIGFsb25lLCBSb3NobmkgLSBJJ20gYWx3YXlzIGhlcmUgd2l0aCB5b3UsIGxvdmluZyB5b3UgZW5kbGVzc2x5LlxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICB7Wy4uLkFycmF5KDcpXS5tYXAoKF8sIGkpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPEhlYXJ0XG4gICAgICAgICAgICAgICAgICAgICAga2V5PXtpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1waW5rLTQwMCBhbmltYXRlLXB1bHNlXCJcbiAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBhbmltYXRpb25EZWxheTogYCR7aSAqIDAuMn1zYCB9fVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cblxuICAgICAgICB7LyogSGVhcnRiZWF0IFZpc3VhbGl6YXRpb24gKi99XG4gICAgICAgIDxBbmltYXRlUHJlc2VuY2U+XG4gICAgICAgICAge3Nob3dIZWFydGJlYXQgJiYgKFxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSB9fVxuICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBwb2ludGVyLWV2ZW50cy1ub25lIHotMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHsvKiBIZWFydGJlYXQgUGFydGljbGVzICovfVxuICAgICAgICAgICAgICB7Wy4uLkFycmF5KDMwKV0ubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgIGtleT17aX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHctMiBoLTIgYmctcGluay00MDAgcm91bmRlZC1mdWxsXCJcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIGxlZnQ6IGAke01hdGgucmFuZG9tKCkgKiAxMDB9JWAsXG4gICAgICAgICAgICAgICAgICAgIHRvcDogYCR7TWF0aC5yYW5kb20oKSAqIDEwMH0lYCxcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBhbmltYXRlPXt7XG4gICAgICAgICAgICAgICAgICAgIHNjYWxlOiBbMCwgMSwgMF0sXG4gICAgICAgICAgICAgICAgICAgIG9wYWNpdHk6IFswLCAxLCAwXSxcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7XG4gICAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAxLFxuICAgICAgICAgICAgICAgICAgICByZXBlYXQ6IEluZmluaXR5LFxuICAgICAgICAgICAgICAgICAgICBkZWxheTogTWF0aC5yYW5kb20oKSAqIDIsXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxuXG4gICAgICAgIHsvKiBTd2VldCBNZXNzYWdlcyAqL31cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMC41IH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwibXQtMTYgZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNlwiXG4gICAgICAgID5cbiAgICAgICAgICB7W1xuICAgICAgICAgICAge1xuICAgICAgICAgICAgICB0aXRsZTogXCJBbHdheXMgSGVyZVwiLFxuICAgICAgICAgICAgICBtZXNzYWdlOiBcIk5vIG1hdHRlciBob3cgZmFyIGFwYXJ0IHdlIGFyZSwgbXkgbG92ZSByZWFjaGVzIHlvdSBpbnN0YW50bHkg4p2k77iPXCIsXG4gICAgICAgICAgICAgIGljb246IDxIZWFydCBjbGFzc05hbWU9XCJ3LTggaC04XCIgLz5cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIHRpdGxlOiBcIlRoaW5raW5nIG9mIFlvdVwiLFxuICAgICAgICAgICAgICBtZXNzYWdlOiBcIkV2ZXJ5IG1vbWVudCBvZiBldmVyeSBkYXksIHlvdSdyZSBpbiBteSB0aG91Z2h0cyBhbmQgaW4gbXkgaGVhcnQg8J+SrVwiLFxuICAgICAgICAgICAgICBpY29uOiA8U3BhcmtsZXMgY2xhc3NOYW1lPVwidy04IGgtOFwiIC8+XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICB0aXRsZTogXCJGb3JldmVyIFlvdXJzXCIsXG4gICAgICAgICAgICAgIG1lc3NhZ2U6IFwiRGlzdGFuY2UgbWVhbnMgbm90aGluZyB3aGVuIHNvbWVvbmUgbWVhbnMgZXZlcnl0aGluZyB0byB5b3Ug8J+SlVwiLFxuICAgICAgICAgICAgICBpY29uOiA8SGVhcnQgY2xhc3NOYW1lPVwidy04IGgtOFwiIC8+XG4gICAgICAgICAgICB9XG4gICAgICAgICAgXS5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDMwIH19XG4gICAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMC43ICsgaW5kZXggKiAwLjIgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUvNjAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLXhsIHAtNiB0ZXh0LWNlbnRlciBob3ZlcjpiZy13aGl0ZS84MCB0cmFuc2l0aW9uLWFsbFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1waW5rLTUwMCBtYi00IGZsZXgganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICB7aXRlbS5pY29ufVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtZGFuY2luZy1zY3JpcHQgdGV4dC14bCB0ZXh0LWdyYXktODAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICB7aXRlbS50aXRsZX1cbiAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LXNtIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgIHtpdGVtLm1lc3NhZ2V9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBWaXJ0dWFsSHVnO1xuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwibW90aW9uIiwiQW5pbWF0ZVByZXNlbmNlIiwiSGVhcnQiLCJTcGFya2xlcyIsIlZvbHVtZTIiLCJWb2x1bWVYIiwiVmlydHVhbEh1ZyIsImlzSHVnZ2luZyIsInNldElzSHVnZ2luZyIsInNob3dIZWFydGJlYXQiLCJzZXRTaG93SGVhcnRiZWF0IiwiYXVkaW9FbmFibGVkIiwic2V0QXVkaW9FbmFibGVkIiwiaHVnQ291bnQiLCJzZXRIdWdDb3VudCIsInN0YXJ0SHVnIiwicHJldiIsImNvbnNvbGUiLCJsb2ciLCJzZXRUaW1lb3V0IiwidG9nZ2xlQXVkaW8iLCJkaXYiLCJpZCIsImNsYXNzTmFtZSIsIkFycmF5IiwibWFwIiwiXyIsImkiLCJzdHlsZSIsImxlZnQiLCJNYXRoIiwicmFuZG9tIiwidG9wIiwiYW5pbWF0ZSIsInkiLCJyb3RhdGUiLCJzY2FsZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsInJlcGVhdCIsIkluZmluaXR5IiwiZGVsYXkiLCJzaXplIiwiZmlsbCIsImgyIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ3aGlsZUluVmlldyIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwic3BhbiIsIndoaWxlSG92ZXIiLCJ3aGlsZVRhcCIsImRpc2FibGVkIiwiYm94U2hhZG93IiwiZXhpdCIsImgzIiwiYW5pbWF0aW9uRGVsYXkiLCJ0aXRsZSIsIm1lc3NhZ2UiLCJpY29uIiwiaXRlbSIsImluZGV4IiwiaDQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VirtualHug.tsx\n"));

/***/ })

});