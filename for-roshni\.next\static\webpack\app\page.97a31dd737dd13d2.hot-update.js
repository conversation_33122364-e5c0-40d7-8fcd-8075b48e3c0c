"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/LoveLetter.tsx":
/*!***************************************!*\
  !*** ./src/components/LoveLetter.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Heart_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Mail,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Mail,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Mail,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst LoveLetter = ()=>{\n    _s();\n    const [isEnvelopeOpen, setIsEnvelopeOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLetter, setShowLetter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const openEnvelope = ()=>{\n        setIsEnvelopeOpen(true);\n        setTimeout(()=>setShowLetter(true), 1000);\n    };\n    const letterContent = \"My Dearest Roshni,\\n\\nAs I write this letter, my heart is overflowing with love for you. You've brought so much color, chaos, warmth, and peace into my life all at once, and I wouldn't have it any other way.\\n\\nYou are my brightest light when everything feels dark, my calmest comfort when the world gets overwhelming, and my biggest blessing when I count all the good things in my life.\\n\\nEvery morning I wake up grateful that I get to love you, and every night I fall asleep with a smile knowing you're mine. Your laugh is my favorite sound, your smile is my favorite sight, and your love is my favorite feeling.\\n\\nYou make the ordinary moments extraordinary just by being in them. Whether we're having deep conversations at 2 AM, sharing silly memes, or just sitting in comfortable silence, every moment with you feels like a gift.\\n\\nOn your special day, I want you to know that you are loved beyond measure. You are cherished, adored, and celebrated not just today, but every single day.\\n\\nYou are my person, my partner, my best friend, and my greatest love. Thank you for being exactly who you are - beautiful, kind, funny, smart, and absolutely perfect in every way.\\n\\nI love you more than words can say, more than actions can show, and more than time can measure.\\n\\nHappy Birthday, my beautiful Babu. Here's to many more years of loving you, laughing with you, and building our beautiful life together.\\n\\nForever and always yours,\\nSaurabh ❤️\\n\\nP.S. - You're stuck with me forever now, so I hope you're ready for a lifetime of my terrible jokes and endless love! \\uD83D\\uDE18\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"letter\",\n        className: \"min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 flex items-center justify-center py-16 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h2, {\n                    initial: {\n                        opacity: 0,\n                        y: -50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-red-600 to-pink-600 mb-8\",\n                    children: \"A Letter For You\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                    initial: {\n                        opacity: 0\n                    },\n                    whileInView: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    className: \"text-xl text-gray-600 mb-12\",\n                    children: \"From my heart to yours, Roshni ✉️\\uD83D\\uDC95\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, undefined),\n                !isEnvelopeOpen ? // Envelope\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        scale: 0.8,\n                        opacity: 0\n                    },\n                    whileInView: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"relative mx-auto cursor-pointer\",\n                    style: {\n                        width: '400px',\n                        height: '280px'\n                    },\n                    onClick: openEnvelope,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-red-100 to-pink-100 rounded-lg shadow-2xl border-2 border-red-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"absolute top-0 left-0 right-0 h-32 bg-gradient-to-br from-red-200 to-pink-200 origin-top\",\n                                    style: {\n                                        clipPath: 'polygon(0 0, 50% 70%, 100% 0)'\n                                    },\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-16 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\",\n                                            fill: \"currentColor\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-8 left-8 right-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-12 h-12 text-red-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-600 font-medium text-lg\",\n                                            children: \"For My Beautiful Roshni\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-sm mt-2\",\n                                            children: \"Click to open ✨\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 pointer-events-none\",\n                            children: [\n                                ...Array(8)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"absolute text-red-300 opacity-60\",\n                                    style: {\n                                        left: \"\".concat(Math.random() * 100, \"%\"),\n                                        top: \"\".concat(Math.random() * 100, \"%\")\n                                    },\n                                    animate: {\n                                        y: [\n                                            0,\n                                            -20,\n                                            0\n                                        ],\n                                        rotate: [\n                                            0,\n                                            360\n                                        ],\n                                        scale: [\n                                            0.8,\n                                            1.2,\n                                            0.8\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 3 + Math.random() * 2,\n                                        repeat: Infinity,\n                                        delay: Math.random() * 2\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        size: 12 + Math.random() * 8,\n                                        fill: \"currentColor\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, i, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, undefined) : // Opened Envelope Animation\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        scale: 0.8,\n                        opacity: 0\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    className: \"relative mx-auto\",\n                    style: {\n                        width: '400px',\n                        height: '280px'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-red-100 to-pink-100 rounded-lg shadow-2xl border-2 border-red-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute top-0 left-0 right-0 h-32 bg-gradient-to-br from-red-200 to-pink-200 origin-top\",\n                            style: {\n                                clipPath: 'polygon(0 0, 50% 70%, 100% 0)'\n                            },\n                            animate: {\n                                rotateX: -180\n                            },\n                            transition: {\n                                duration: 1,\n                                ease: \"easeInOut\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                    children: showLetter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 100\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 1,\n                            ease: \"easeOut\"\n                        },\n                        className: \"mt-16 max-w-3xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl shadow-2xl p-8 md:p-12 border border-red-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-8 h-8 text-red-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-3xl font-dancing-script text-red-600 mb-2\",\n                                            children: \"My Love Letter to You\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-24 h-0.5 bg-gradient-to-r from-red-300 to-pink-300 mx-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-left space-y-4 text-gray-700 leading-relaxed\",\n                                    children: letterContent.split('\\n\\n').map((paragraph, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                delay: index * 0.3,\n                                                duration: 0.6\n                                            },\n                                            className: \"\".concat(paragraph.includes('My Dearest Roshni') ? 'font-dancing-script text-xl text-red-600' : paragraph.includes('Forever and always yours') ? 'font-dancing-script text-lg text-red-600 text-right mt-8' : paragraph.includes('P.S.') ? 'text-sm text-gray-500 italic' : ''),\n                                            children: paragraph\n                                        }, index, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        opacity: 0\n                                    },\n                                    animate: {\n                                        opacity: 1\n                                    },\n                                    transition: {\n                                        delay: 2\n                                    },\n                                    className: \"text-center mt-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center space-x-2 mb-4\",\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-4 h-4 text-red-400 animate-pulse\",\n                                                    fill: \"currentColor\",\n                                                    style: {\n                                                        animationDelay: \"\".concat(i * 0.2, \"s\")\n                                                    }\n                                                }, i, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 font-medium\",\n                                            children: \"With all my love, today and always ❤️\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LoveLetter.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LoveLetter, \"PHBztXQosJyJui2jgVw3eNksmC0=\");\n_c = LoveLetter;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoveLetter);\nvar _c;\n$RefreshReg$(_c, \"LoveLetter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0xvdmVMZXR0ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ1k7QUFDSDtBQUVyRCxNQUFNTSxhQUFhOztJQUNqQixNQUFNLENBQUNDLGdCQUFnQkMsa0JBQWtCLEdBQUdSLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ1MsWUFBWUMsY0FBYyxHQUFHViwrQ0FBUUEsQ0FBQztJQUU3QyxNQUFNVyxlQUFlO1FBQ25CSCxrQkFBa0I7UUFDbEJJLFdBQVcsSUFBTUYsY0FBYyxPQUFPO0lBQ3hDO0lBRUEsTUFBTUcsZ0JBQWlCO0lBdUJ2QixxQkFDRSw4REFBQ0M7UUFBSUMsSUFBRztRQUFTQyxXQUFVO2tCQUN6Qiw0RUFBQ0Y7WUFBSUUsV0FBVTs7OEJBQ2IsOERBQUNmLGlEQUFNQSxDQUFDZ0IsRUFBRTtvQkFDUkMsU0FBUzt3QkFBRUMsU0FBUzt3QkFBR0MsR0FBRyxDQUFDO29CQUFHO29CQUM5QkMsYUFBYTt3QkFBRUYsU0FBUzt3QkFBR0MsR0FBRztvQkFBRTtvQkFDaENFLFlBQVk7d0JBQUVDLFVBQVU7b0JBQUk7b0JBQzVCUCxXQUFVOzhCQUNYOzs7Ozs7OEJBSUQsOERBQUNmLGlEQUFNQSxDQUFDdUIsQ0FBQztvQkFDUE4sU0FBUzt3QkFBRUMsU0FBUztvQkFBRTtvQkFDdEJFLGFBQWE7d0JBQUVGLFNBQVM7b0JBQUU7b0JBQzFCRyxZQUFZO3dCQUFFRyxPQUFPO29CQUFJO29CQUN6QlQsV0FBVTs4QkFDWDs7Ozs7O2dCQUlBLENBQUNULGlCQUNBLFdBQVc7OEJBQ1gsOERBQUNOLGlEQUFNQSxDQUFDYSxHQUFHO29CQUNUSSxTQUFTO3dCQUFFUSxPQUFPO3dCQUFLUCxTQUFTO29CQUFFO29CQUNsQ0UsYUFBYTt3QkFBRUssT0FBTzt3QkFBR1AsU0FBUztvQkFBRTtvQkFDcENHLFlBQVk7d0JBQUVDLFVBQVU7b0JBQUk7b0JBQzVCUCxXQUFVO29CQUNWVyxPQUFPO3dCQUFFQyxPQUFPO3dCQUFTQyxRQUFRO29CQUFRO29CQUN6Q0MsU0FBU25COztzQ0FHVCw4REFBQ0c7NEJBQUlFLFdBQVU7OzhDQUViLDhEQUFDZixpREFBTUEsQ0FBQ2EsR0FBRztvQ0FDVEUsV0FBVTtvQ0FDVlcsT0FBTzt3Q0FDTEksVUFBVTtvQ0FDWjtvQ0FDQUMsWUFBWTt3Q0FBRU4sT0FBTztvQ0FBSztvQ0FDMUJKLFlBQVk7d0NBQUVDLFVBQVU7b0NBQUk7OENBRzVCLDRFQUFDVDt3Q0FBSUUsV0FBVTtrREFDYiw0RUFBQ1osK0ZBQUtBOzRDQUFDWSxXQUFVOzRDQUFxQmlCLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSy9DLDhEQUFDbkI7b0NBQUlFLFdBQVU7O3NEQUNiLDhEQUFDYiwrRkFBSUE7NENBQUNhLFdBQVU7Ozs7OztzREFDaEIsOERBQUNROzRDQUFFUixXQUFVO3NEQUFtQzs7Ozs7O3NEQUNoRCw4REFBQ1E7NENBQUVSLFdBQVU7c0RBQTRCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSzdDLDhEQUFDRjs0QkFBSUUsV0FBVTtzQ0FDWjttQ0FBSWtCLE1BQU07NkJBQUcsQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLEdBQUdDLGtCQUNyQiw4REFBQ3BDLGlEQUFNQSxDQUFDYSxHQUFHO29DQUVURSxXQUFVO29DQUNWVyxPQUFPO3dDQUNMVyxNQUFNLEdBQXVCLE9BQXBCQyxLQUFLQyxNQUFNLEtBQUssS0FBSTt3Q0FDN0JDLEtBQUssR0FBdUIsT0FBcEJGLEtBQUtDLE1BQU0sS0FBSyxLQUFJO29DQUM5QjtvQ0FDQUUsU0FBUzt3Q0FDUHRCLEdBQUc7NENBQUM7NENBQUcsQ0FBQzs0Q0FBSTt5Q0FBRTt3Q0FDZHVCLFFBQVE7NENBQUM7NENBQUc7eUNBQUk7d0NBQ2hCakIsT0FBTzs0Q0FBQzs0Q0FBSzs0Q0FBSzt5Q0FBSTtvQ0FDeEI7b0NBQ0FKLFlBQVk7d0NBQ1ZDLFVBQVUsSUFBSWdCLEtBQUtDLE1BQU0sS0FBSzt3Q0FDOUJJLFFBQVFDO3dDQUNScEIsT0FBT2MsS0FBS0MsTUFBTSxLQUFLO29DQUN6Qjs4Q0FFQSw0RUFBQ3BDLCtGQUFLQTt3Q0FBQzBDLE1BQU0sS0FBS1AsS0FBS0MsTUFBTSxLQUFLO3dDQUFHUCxNQUFLOzs7Ozs7bUNBakJyQ0k7Ozs7Ozs7Ozs7Ozs7OztnQ0F1QmIsNEJBQTRCOzhCQUM1Qiw4REFBQ3BDLGlEQUFNQSxDQUFDYSxHQUFHO29CQUNUSSxTQUFTO3dCQUFFUSxPQUFPO3dCQUFLUCxTQUFTO29CQUFFO29CQUNsQ3VCLFNBQVM7d0JBQUVoQixPQUFPO3dCQUFHUCxTQUFTO29CQUFFO29CQUNoQ0gsV0FBVTtvQkFDVlcsT0FBTzt3QkFBRUMsT0FBTzt3QkFBU0MsUUFBUTtvQkFBUTs4QkFHekMsNEVBQUNmO3dCQUFJRSxXQUFVO2tDQUViLDRFQUFDZixpREFBTUEsQ0FBQ2EsR0FBRzs0QkFDVEUsV0FBVTs0QkFDVlcsT0FBTztnQ0FDTEksVUFBVTs0QkFDWjs0QkFDQVcsU0FBUztnQ0FBRUssU0FBUyxDQUFDOzRCQUFJOzRCQUN6QnpCLFlBQVk7Z0NBQUVDLFVBQVU7Z0NBQUd5QixNQUFNOzRCQUFZOzs7Ozs7Ozs7Ozs7Ozs7OzhCQU9yRCw4REFBQzlDLDBEQUFlQTs4QkFDYk8sNEJBQ0MsOERBQUNSLGlEQUFNQSxDQUFDYSxHQUFHO3dCQUNUSSxTQUFTOzRCQUFFQyxTQUFTOzRCQUFHQyxHQUFHO3dCQUFJO3dCQUM5QnNCLFNBQVM7NEJBQUV2QixTQUFTOzRCQUFHQyxHQUFHO3dCQUFFO3dCQUM1QkUsWUFBWTs0QkFBRUMsVUFBVTs0QkFBR3lCLE1BQU07d0JBQVU7d0JBQzNDaEMsV0FBVTtrQ0FFViw0RUFBQ0Y7NEJBQUlFLFdBQVU7OzhDQUViLDhEQUFDRjtvQ0FBSUUsV0FBVTs7c0RBQ2IsOERBQUNYLCtGQUFRQTs0Q0FBQ1csV0FBVTs7Ozs7O3NEQUNwQiw4REFBQ2lDOzRDQUFHakMsV0FBVTtzREFBaUQ7Ozs7OztzREFHL0QsOERBQUNGOzRDQUFJRSxXQUFVOzs7Ozs7Ozs7Ozs7OENBSWpCLDhEQUFDRjtvQ0FBSUUsV0FBVTs4Q0FDWkgsY0FBY3FDLEtBQUssQ0FBQyxRQUFRZixHQUFHLENBQUMsQ0FBQ2dCLFdBQVdDLHNCQUMzQyw4REFBQ25ELGlEQUFNQSxDQUFDdUIsQ0FBQzs0Q0FFUE4sU0FBUztnREFBRUMsU0FBUztnREFBR2tDLEdBQUcsQ0FBQzs0Q0FBRzs0Q0FDOUJYLFNBQVM7Z0RBQUV2QixTQUFTO2dEQUFHa0MsR0FBRzs0Q0FBRTs0Q0FDNUIvQixZQUFZO2dEQUFFRyxPQUFPMkIsUUFBUTtnREFBSzdCLFVBQVU7NENBQUk7NENBQ2hEUCxXQUFXLEdBUVYsT0FQQ21DLFVBQVVHLFFBQVEsQ0FBQyx1QkFDZiw2Q0FDQUgsVUFBVUcsUUFBUSxDQUFDLDhCQUNuQiw2REFDQUgsVUFBVUcsUUFBUSxDQUFDLFVBQ25CLGlDQUNBO3NEQUdMSDsyQ0FkSUM7Ozs7Ozs7Ozs7OENBb0JYLDhEQUFDbkQsaURBQU1BLENBQUNhLEdBQUc7b0NBQ1RJLFNBQVM7d0NBQUVDLFNBQVM7b0NBQUU7b0NBQ3RCdUIsU0FBUzt3Q0FBRXZCLFNBQVM7b0NBQUU7b0NBQ3RCRyxZQUFZO3dDQUFFRyxPQUFPO29DQUFFO29DQUN2QlQsV0FBVTs7c0RBRVYsOERBQUNGOzRDQUFJRSxXQUFVO3NEQUNaO21EQUFJa0IsTUFBTTs2Q0FBRyxDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsR0FBR0Msa0JBQ3JCLDhEQUFDakMsK0ZBQUtBO29EQUVKWSxXQUFVO29EQUNWaUIsTUFBSztvREFDTE4sT0FBTzt3REFBRTRCLGdCQUFnQixHQUFXLE9BQVJsQixJQUFJLEtBQUk7b0RBQUc7bURBSGxDQTs7Ozs7Ozs7OztzREFPWCw4REFBQ2I7NENBQUVSLFdBQVU7c0RBQTJCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFXMUQ7R0EvTU1WO0tBQUFBO0FBaU5OLGlFQUFlQSxVQUFVQSxFQUFDIiwic291cmNlcyI6WyJEOlxcUk9TSE5JXFxmb3JSb3NobmlcXGZvci1yb3NobmlcXHNyY1xcY29tcG9uZW50c1xcTG92ZUxldHRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tICdmcmFtZXItbW90aW9uJztcbmltcG9ydCB7IE1haWwsIEhlYXJ0LCBTcGFya2xlcyB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5cbmNvbnN0IExvdmVMZXR0ZXIgPSAoKSA9PiB7XG4gIGNvbnN0IFtpc0VudmVsb3BlT3Blbiwgc2V0SXNFbnZlbG9wZU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd0xldHRlciwgc2V0U2hvd0xldHRlcl0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgY29uc3Qgb3BlbkVudmVsb3BlID0gKCkgPT4ge1xuICAgIHNldElzRW52ZWxvcGVPcGVuKHRydWUpO1xuICAgIHNldFRpbWVvdXQoKCkgPT4gc2V0U2hvd0xldHRlcih0cnVlKSwgMTAwMCk7XG4gIH07XG5cbiAgY29uc3QgbGV0dGVyQ29udGVudCA9IGBNeSBEZWFyZXN0IFJvc2huaSxcblxuQXMgSSB3cml0ZSB0aGlzIGxldHRlciwgbXkgaGVhcnQgaXMgb3ZlcmZsb3dpbmcgd2l0aCBsb3ZlIGZvciB5b3UuIFlvdSd2ZSBicm91Z2h0IHNvIG11Y2ggY29sb3IsIGNoYW9zLCB3YXJtdGgsIGFuZCBwZWFjZSBpbnRvIG15IGxpZmUgYWxsIGF0IG9uY2UsIGFuZCBJIHdvdWxkbid0IGhhdmUgaXQgYW55IG90aGVyIHdheS5cblxuWW91IGFyZSBteSBicmlnaHRlc3QgbGlnaHQgd2hlbiBldmVyeXRoaW5nIGZlZWxzIGRhcmssIG15IGNhbG1lc3QgY29tZm9ydCB3aGVuIHRoZSB3b3JsZCBnZXRzIG92ZXJ3aGVsbWluZywgYW5kIG15IGJpZ2dlc3QgYmxlc3Npbmcgd2hlbiBJIGNvdW50IGFsbCB0aGUgZ29vZCB0aGluZ3MgaW4gbXkgbGlmZS5cblxuRXZlcnkgbW9ybmluZyBJIHdha2UgdXAgZ3JhdGVmdWwgdGhhdCBJIGdldCB0byBsb3ZlIHlvdSwgYW5kIGV2ZXJ5IG5pZ2h0IEkgZmFsbCBhc2xlZXAgd2l0aCBhIHNtaWxlIGtub3dpbmcgeW91J3JlIG1pbmUuIFlvdXIgbGF1Z2ggaXMgbXkgZmF2b3JpdGUgc291bmQsIHlvdXIgc21pbGUgaXMgbXkgZmF2b3JpdGUgc2lnaHQsIGFuZCB5b3VyIGxvdmUgaXMgbXkgZmF2b3JpdGUgZmVlbGluZy5cblxuWW91IG1ha2UgdGhlIG9yZGluYXJ5IG1vbWVudHMgZXh0cmFvcmRpbmFyeSBqdXN0IGJ5IGJlaW5nIGluIHRoZW0uIFdoZXRoZXIgd2UncmUgaGF2aW5nIGRlZXAgY29udmVyc2F0aW9ucyBhdCAyIEFNLCBzaGFyaW5nIHNpbGx5IG1lbWVzLCBvciBqdXN0IHNpdHRpbmcgaW4gY29tZm9ydGFibGUgc2lsZW5jZSwgZXZlcnkgbW9tZW50IHdpdGggeW91IGZlZWxzIGxpa2UgYSBnaWZ0LlxuXG5PbiB5b3VyIHNwZWNpYWwgZGF5LCBJIHdhbnQgeW91IHRvIGtub3cgdGhhdCB5b3UgYXJlIGxvdmVkIGJleW9uZCBtZWFzdXJlLiBZb3UgYXJlIGNoZXJpc2hlZCwgYWRvcmVkLCBhbmQgY2VsZWJyYXRlZCBub3QganVzdCB0b2RheSwgYnV0IGV2ZXJ5IHNpbmdsZSBkYXkuXG5cbllvdSBhcmUgbXkgcGVyc29uLCBteSBwYXJ0bmVyLCBteSBiZXN0IGZyaWVuZCwgYW5kIG15IGdyZWF0ZXN0IGxvdmUuIFRoYW5rIHlvdSBmb3IgYmVpbmcgZXhhY3RseSB3aG8geW91IGFyZSAtIGJlYXV0aWZ1bCwga2luZCwgZnVubnksIHNtYXJ0LCBhbmQgYWJzb2x1dGVseSBwZXJmZWN0IGluIGV2ZXJ5IHdheS5cblxuSSBsb3ZlIHlvdSBtb3JlIHRoYW4gd29yZHMgY2FuIHNheSwgbW9yZSB0aGFuIGFjdGlvbnMgY2FuIHNob3csIGFuZCBtb3JlIHRoYW4gdGltZSBjYW4gbWVhc3VyZS5cblxuSGFwcHkgQmlydGhkYXksIG15IGJlYXV0aWZ1bCBCYWJ1LiBIZXJlJ3MgdG8gbWFueSBtb3JlIHllYXJzIG9mIGxvdmluZyB5b3UsIGxhdWdoaW5nIHdpdGggeW91LCBhbmQgYnVpbGRpbmcgb3VyIGJlYXV0aWZ1bCBsaWZlIHRvZ2V0aGVyLlxuXG5Gb3JldmVyIGFuZCBhbHdheXMgeW91cnMsXG5TYXVyYWJoIOKdpO+4j1xuXG5QLlMuIC0gWW91J3JlIHN0dWNrIHdpdGggbWUgZm9yZXZlciBub3csIHNvIEkgaG9wZSB5b3UncmUgcmVhZHkgZm9yIGEgbGlmZXRpbWUgb2YgbXkgdGVycmlibGUgam9rZXMgYW5kIGVuZGxlc3MgbG92ZSEg8J+YmGA7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGlkPVwibGV0dGVyXCIgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYW1iZXItNTAgdmlhLW9yYW5nZS01MCB0by1yZWQtNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHktMTYgcHgtNFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0byB0ZXh0LWNlbnRlclwiPlxuICAgICAgICA8bW90aW9uLmgyXG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAtNTAgfX1cbiAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44IH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC01eGwgbWQ6dGV4dC03eGwgZm9udC1kYW5jaW5nLXNjcmlwdCB0ZXh0LXRyYW5zcGFyZW50IGJnLWNsaXAtdGV4dCBiZy1ncmFkaWVudC10by1yIGZyb20tcmVkLTYwMCB0by1waW5rLTYwMCBtYi04XCJcbiAgICAgICAgPlxuICAgICAgICAgIEEgTGV0dGVyIEZvciBZb3VcbiAgICAgICAgPC9tb3Rpb24uaDI+XG5cbiAgICAgICAgPG1vdGlvbi5wXG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSB9fVxuICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuMyB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTYwMCBtYi0xMlwiXG4gICAgICAgID5cbiAgICAgICAgICBGcm9tIG15IGhlYXJ0IHRvIHlvdXJzLCBSb3Nobmkg4pyJ77iP8J+SlVxuICAgICAgICA8L21vdGlvbi5wPlxuXG4gICAgICAgIHshaXNFbnZlbG9wZU9wZW4gPyAoXG4gICAgICAgICAgLy8gRW52ZWxvcGVcbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgaW5pdGlhbD17eyBzY2FsZTogMC44LCBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgICB3aGlsZUluVmlldz17eyBzY2FsZTogMSwgb3BhY2l0eTogMSB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44IH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSBteC1hdXRvIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAnNDAwcHgnLCBoZWlnaHQ6ICcyODBweCcgfX1cbiAgICAgICAgICAgIG9uQ2xpY2s9e29wZW5FbnZlbG9wZX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7LyogRW52ZWxvcGUgQm9keSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1iciBmcm9tLXJlZC0xMDAgdG8tcGluay0xMDAgcm91bmRlZC1sZyBzaGFkb3ctMnhsIGJvcmRlci0yIGJvcmRlci1yZWQtMjAwXCI+XG4gICAgICAgICAgICAgIHsvKiBFbnZlbG9wZSBGbGFwICovfVxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0wIGxlZnQtMCByaWdodC0wIGgtMzIgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1yZWQtMjAwIHRvLXBpbmstMjAwIG9yaWdpbi10b3BcIlxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICBjbGlwUGF0aDogJ3BvbHlnb24oMCAwLCA1MCUgNzAlLCAxMDAlIDApJyxcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDUgfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjMgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHsvKiBXYXggU2VhbCAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xNiBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMiB3LTEyIGgtMTIgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1yZWQtNTAwIHRvLXJlZC02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPEhlYXJ0IGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC13aGl0ZVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICAgICAgey8qIEVudmVsb3BlIENvbnRlbnQgUHJldmlldyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tOCBsZWZ0LTggcmlnaHQtOCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxNYWlsIGNsYXNzTmFtZT1cInctMTIgaC0xMiB0ZXh0LXJlZC00MDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgZm9udC1tZWRpdW0gdGV4dC1sZ1wiPkZvciBNeSBCZWF1dGlmdWwgUm9zaG5pPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LXNtIG10LTJcIj5DbGljayB0byBvcGVuIOKcqDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEZsb2F0aW5nIEhlYXJ0cyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBwb2ludGVyLWV2ZW50cy1ub25lXCI+XG4gICAgICAgICAgICAgIHtbLi4uQXJyYXkoOCldLm1hcCgoXywgaSkgPT4gKFxuICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICBrZXk9e2l9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0ZXh0LXJlZC0zMDAgb3BhY2l0eS02MFwiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBsZWZ0OiBgJHtNYXRoLnJhbmRvbSgpICogMTAwfSVgLFxuICAgICAgICAgICAgICAgICAgICB0b3A6IGAke01hdGgucmFuZG9tKCkgKiAxMDB9JWAsXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17e1xuICAgICAgICAgICAgICAgICAgICB5OiBbMCwgLTIwLCAwXSxcbiAgICAgICAgICAgICAgICAgICAgcm90YXRlOiBbMCwgMzYwXSxcbiAgICAgICAgICAgICAgICAgICAgc2NhbGU6IFswLjgsIDEuMiwgMC44XSxcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7XG4gICAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAzICsgTWF0aC5yYW5kb20oKSAqIDIsXG4gICAgICAgICAgICAgICAgICAgIHJlcGVhdDogSW5maW5pdHksXG4gICAgICAgICAgICAgICAgICAgIGRlbGF5OiBNYXRoLnJhbmRvbSgpICogMixcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPEhlYXJ0IHNpemU9ezEyICsgTWF0aC5yYW5kb20oKSAqIDh9IGZpbGw9XCJjdXJyZW50Q29sb3JcIiAvPlxuICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgLy8gT3BlbmVkIEVudmVsb3BlIEFuaW1hdGlvblxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IHNjYWxlOiAwLjgsIG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgc2NhbGU6IDEsIG9wYWNpdHk6IDEgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIG14LWF1dG9cIlxuICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6ICc0MDBweCcsIGhlaWdodDogJzI4MHB4JyB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHsvKiBFbnZlbG9wZSBCb2R5IChvcGVuZWQpICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcmVkLTEwMCB0by1waW5rLTEwMCByb3VuZGVkLWxnIHNoYWRvdy0yeGwgYm9yZGVyLTIgYm9yZGVyLXJlZC0yMDBcIj5cbiAgICAgICAgICAgICAgey8qIE9wZW5lZCBGbGFwICovfVxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0wIGxlZnQtMCByaWdodC0wIGgtMzIgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1yZWQtMjAwIHRvLXBpbmstMjAwIG9yaWdpbi10b3BcIlxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICBjbGlwUGF0aDogJ3BvbHlnb24oMCAwLCA1MCUgNzAlLCAxMDAlIDApJyxcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgcm90YXRlWDogLTE4MCB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDEsIGVhc2U6IFwiZWFzZUluT3V0XCIgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogTGV0dGVyIENvbnRlbnQgKi99XG4gICAgICAgIDxBbmltYXRlUHJlc2VuY2U+XG4gICAgICAgICAge3Nob3dMZXR0ZXIgJiYgKFxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAxMDAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDEsIGVhc2U6IFwiZWFzZU91dFwiIH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTE2IG1heC13LTN4bCBteC1hdXRvXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLTJ4bCBzaGFkb3ctMnhsIHAtOCBtZDpwLTEyIGJvcmRlciBib3JkZXItcmVkLTEwMFwiPlxuICAgICAgICAgICAgICAgIHsvKiBMZXR0ZXIgSGVhZGVyICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItOFwiPlxuICAgICAgICAgICAgICAgICAgPFNwYXJrbGVzIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC1yZWQtNDAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1kYW5jaW5nLXNjcmlwdCB0ZXh0LXJlZC02MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICBNeSBMb3ZlIExldHRlciB0byBZb3VcbiAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjQgaC0wLjUgYmctZ3JhZGllbnQtdG8tciBmcm9tLXJlZC0zMDAgdG8tcGluay0zMDAgbXgtYXV0b1wiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIExldHRlciBCb2R5ICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZWZ0IHNwYWNlLXktNCB0ZXh0LWdyYXktNzAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAge2xldHRlckNvbnRlbnQuc3BsaXQoJ1xcblxcbicpLm1hcCgocGFyYWdyYXBoLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8bW90aW9uLnBcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjMsIGR1cmF0aW9uOiAwLjYgfX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Ake1xuICAgICAgICAgICAgICAgICAgICAgICAgcGFyYWdyYXBoLmluY2x1ZGVzKCdNeSBEZWFyZXN0IFJvc2huaScpIFxuICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdmb250LWRhbmNpbmctc2NyaXB0IHRleHQteGwgdGV4dC1yZWQtNjAwJyBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBwYXJhZ3JhcGguaW5jbHVkZXMoJ0ZvcmV2ZXIgYW5kIGFsd2F5cyB5b3VycycpXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2ZvbnQtZGFuY2luZy1zY3JpcHQgdGV4dC1sZyB0ZXh0LXJlZC02MDAgdGV4dC1yaWdodCBtdC04J1xuICAgICAgICAgICAgICAgICAgICAgICAgICA6IHBhcmFncmFwaC5pbmNsdWRlcygnUC5TLicpXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gJ3RleHQtc20gdGV4dC1ncmF5LTUwMCBpdGFsaWMnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogJydcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtwYXJhZ3JhcGh9XG4gICAgICAgICAgICAgICAgICAgIDwvbW90aW9uLnA+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBMZXR0ZXIgRm9vdGVyICovfVxuICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSB9fVxuICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMiB9fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbXQtMTJcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTIgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICB7Wy4uLkFycmF5KDUpXS5tYXAoKF8sIGkpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8SGVhcnRcbiAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1yZWQtNDAwIGFuaW1hdGUtcHVsc2VcIlxuICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBhbmltYXRpb25EZWxheTogYCR7aSAqIDAuMn1zYCB9fVxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgV2l0aCBhbGwgbXkgbG92ZSwgdG9kYXkgYW5kIGFsd2F5cyDinaTvuI9cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBMb3ZlTGV0dGVyO1xuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwibW90aW9uIiwiQW5pbWF0ZVByZXNlbmNlIiwiTWFpbCIsIkhlYXJ0IiwiU3BhcmtsZXMiLCJMb3ZlTGV0dGVyIiwiaXNFbnZlbG9wZU9wZW4iLCJzZXRJc0VudmVsb3BlT3BlbiIsInNob3dMZXR0ZXIiLCJzZXRTaG93TGV0dGVyIiwib3BlbkVudmVsb3BlIiwic2V0VGltZW91dCIsImxldHRlckNvbnRlbnQiLCJkaXYiLCJpZCIsImNsYXNzTmFtZSIsImgyIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5Iiwid2hpbGVJblZpZXciLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJwIiwiZGVsYXkiLCJzY2FsZSIsInN0eWxlIiwid2lkdGgiLCJoZWlnaHQiLCJvbkNsaWNrIiwiY2xpcFBhdGgiLCJ3aGlsZUhvdmVyIiwiZmlsbCIsIkFycmF5IiwibWFwIiwiXyIsImkiLCJsZWZ0IiwiTWF0aCIsInJhbmRvbSIsInRvcCIsImFuaW1hdGUiLCJyb3RhdGUiLCJyZXBlYXQiLCJJbmZpbml0eSIsInNpemUiLCJyb3RhdGVYIiwiZWFzZSIsImgzIiwic3BsaXQiLCJwYXJhZ3JhcGgiLCJpbmRleCIsIngiLCJpbmNsdWRlcyIsImFuaW1hdGlvbkRlbGF5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LoveLetter.tsx\n"));

/***/ })

});