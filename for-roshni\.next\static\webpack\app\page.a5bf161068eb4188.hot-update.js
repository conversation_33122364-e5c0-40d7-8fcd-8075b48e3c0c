"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/LandingPage.tsx":
/*!****************************************!*\
  !*** ./src/components/LandingPage.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _ParticleBackground__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ParticleBackground */ \"(app-pages-browser)/./src/components/ParticleBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst LandingPage = ()=>{\n    _s();\n    const [currentText, setCurrentText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showCursor, setShowCursor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const messages = [\n        \"Hi Roshni... 💖\",\n        \"Happy Birthday, Jaan... 🎂\",\n        \"This website is just for you...\",\n        \"To celebrate you... to love you... forever ❤️\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (currentIndex < messages.length) {\n                const message = messages[currentIndex];\n                let charIndex = 0;\n                const typeInterval = setInterval({\n                    \"LandingPage.useEffect.typeInterval\": ()=>{\n                        if (charIndex <= message.length) {\n                            setCurrentText(message.slice(0, charIndex));\n                            charIndex++;\n                        } else {\n                            clearInterval(typeInterval);\n                            setTimeout({\n                                \"LandingPage.useEffect.typeInterval\": ()=>{\n                                    if (currentIndex < messages.length - 1) {\n                                        setCurrentIndex(currentIndex + 1);\n                                        setCurrentText('');\n                                    }\n                                }\n                            }[\"LandingPage.useEffect.typeInterval\"], 2000);\n                        }\n                    }\n                }[\"LandingPage.useEffect.typeInterval\"], 100);\n                return ({\n                    \"LandingPage.useEffect\": ()=>clearInterval(typeInterval)\n                })[\"LandingPage.useEffect\"];\n            }\n        }\n    }[\"LandingPage.useEffect\"], [\n        currentIndex\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            const cursorInterval = setInterval({\n                \"LandingPage.useEffect.cursorInterval\": ()=>{\n                    setShowCursor({\n                        \"LandingPage.useEffect.cursorInterval\": (prev)=>!prev\n                    }[\"LandingPage.useEffect.cursorInterval\"]);\n                }\n            }[\"LandingPage.useEffect.cursorInterval\"], 500);\n            return ({\n                \"LandingPage.useEffect\": ()=>clearInterval(cursorInterval)\n            })[\"LandingPage.useEffect\"];\n        }\n    }[\"LandingPage.useEffect\"], []);\n    const scrollToNext = ()=>{\n        const nextSection = document.getElementById('celebration');\n        if (nextSection) {\n            nextSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"landing\",\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ParticleBackground__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    [\n                        ...Array(25)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            className: \"absolute\",\n                            style: {\n                                left: \"\".concat(Math.random() * 100, \"%\"),\n                                top: \"\".concat(Math.random() * 100, \"%\")\n                            },\n                            animate: {\n                                y: [\n                                    0,\n                                    -50,\n                                    0\n                                ],\n                                x: [\n                                    0,\n                                    Math.random() * 30 - 15,\n                                    0\n                                ],\n                                rotate: [\n                                    0,\n                                    360\n                                ],\n                                scale: [\n                                    0.3,\n                                    1.5,\n                                    0.3\n                                ]\n                            },\n                            transition: {\n                                duration: 4 + Math.random() * 3,\n                                repeat: Infinity,\n                                delay: Math.random() * 3,\n                                ease: \"easeInOut\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-full blur-sm opacity-20\",\n                                style: {\n                                    width: \"\".concat(20 + Math.random() * 40, \"px\"),\n                                    height: \"\".concat(20 + Math.random() * 40, \"px\"),\n                                    background: \"linear-gradient(45deg,\\n                  \".concat([\n                                        '#ff6b9d',\n                                        '#c44569',\n                                        '#f8b500',\n                                        '#ff9ff3',\n                                        '#f368e0'\n                                    ][Math.floor(Math.random() * 5)], \",\\n                  \").concat([\n                                        '#ff6b9d',\n                                        '#c44569',\n                                        '#f8b500',\n                                        '#ff9ff3',\n                                        '#f368e0'\n                                    ][Math.floor(Math.random() * 5)], \"\\n                )\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined)\n                        }, i, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined)),\n                    [\n                        ...Array(8)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            className: \"absolute text-pink-300 opacity-40\",\n                            style: {\n                                left: \"\".concat(Math.random() * 100, \"%\"),\n                                top: \"\".concat(Math.random() * 100, \"%\")\n                            },\n                            animate: {\n                                y: [\n                                    0,\n                                    -40,\n                                    0\n                                ],\n                                rotate: [\n                                    0,\n                                    360\n                                ],\n                                scale: [\n                                    0.8,\n                                    1.3,\n                                    0.8\n                                ]\n                            },\n                            transition: {\n                                duration: 5 + Math.random() * 2,\n                                repeat: Infinity,\n                                delay: Math.random() * 3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: 15 + Math.random() * 15,\n                                fill: \"currentColor\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, undefined)\n                        }, \"heart-\".concat(i), false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center z-10 px-4 max-w-5xl mx-auto relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-radial from-pink-200/20 via-purple-200/10 to-transparent rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 1.2,\n                            ease: \"easeOut\"\n                        },\n                        className: \"mb-12 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h1, {\n                                className: \"text-7xl md:text-9xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-pink-500 via-purple-500 to-rose-500 mb-6 leading-tight\",\n                                animate: {\n                                    backgroundPosition: [\n                                        '0% 50%',\n                                        '100% 50%',\n                                        '0% 50%'\n                                    ]\n                                },\n                                transition: {\n                                    duration: 8,\n                                    repeat: Infinity,\n                                    ease: \"linear\"\n                                },\n                                style: {\n                                    backgroundSize: '200% 200%'\n                                },\n                                children: \"Happy Birthday\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    scale: 0.8,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    scale: 1,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 0.5,\n                                    duration: 0.8\n                                },\n                                className: \"relative mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-rose-600 to-pink-600 mb-2\",\n                                        children: \"Roshni Jwala\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        animate: {\n                                            scale: [\n                                                1,\n                                                1.05,\n                                                1\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            repeat: Infinity\n                                        },\n                                        className: \"text-xl md:text-2xl text-gray-600 font-medium\",\n                                        children: \"My Beautiful Babu ✨\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 1,\n                                    duration: 0.8\n                                },\n                                className: \"text-lg md:text-xl text-gray-500 max-w-2xl mx-auto leading-relaxed\",\n                                children: \"This is your magical space. Click. Explore. Feel loved. \\uD83D\\uDC95\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-[120px] flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                delay: 1.5\n                            },\n                            className: \"text-xl md:text-2xl text-gray-600 font-medium\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-block\",\n                                children: [\n                                    currentText,\n                                    showCursor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block w-0.5 h-6 bg-pink-500 ml-1 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, undefined),\n                    currentIndex >= messages.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 2\n                        },\n                        className: \"mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: scrollToNext,\n                            className: \"group flex flex-col items-center text-pink-500 hover:text-pink-600 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"Scroll Down\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            10,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 32,\n                                        className: \"group-hover:scale-110 transition-transform\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: 3\n                },\n                className: \"absolute bottom-8 right-8 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"bg-white/20 backdrop-blur-sm rounded-full p-3 text-pink-500 hover:bg-white/30 transition-all\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: 24,\n                        className: \"animate-heartbeat\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LandingPage, \"zghU/jnPq1e8QD9Kjsgxt3LSjTE=\");\n_c = LandingPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LandingPage);\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LandingPage.tsx\n"));

/***/ })

});