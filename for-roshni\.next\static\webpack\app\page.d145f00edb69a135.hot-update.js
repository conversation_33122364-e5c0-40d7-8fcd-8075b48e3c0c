"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/FutureDreams.tsx":
/*!*****************************************!*\
  !*** ./src/components/FutureDreams.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Crown,Heart,Home,Plane,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plane.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Crown,Heart,Home,Plane,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Crown,Heart,Home,Plane,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Crown,Heart,Home,Plane,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/baby.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Crown,Heart,Home,Plane,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Crown,Heart,Home,Plane,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Crown,Heart,Home,Plane,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst FutureDreams = ()=>{\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        -100\n    ]);\n    const opacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        0.2,\n        0.8,\n        1\n    ], [\n        0,\n        1,\n        1,\n        0\n    ]);\n    const dreams = [\n        {\n            id: 1,\n            title: \"Travel the World Together\",\n            description: \"Exploring beautiful destinations, creating memories in every corner of the world\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 31,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-blue-400 to-cyan-500\",\n            details: \"Paris for romance, Tokyo for adventure, Maldives for relaxation, and so many more places to discover together, hand in hand.\"\n        },\n        {\n            id: 2,\n            title: \"Our Dream Home\",\n            description: \"A cozy place where we can build our life together, filled with love and laughter\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 39,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-green-400 to-emerald-500\",\n            details: \"A beautiful home with a garden where we can have morning coffee together, a cozy living room for movie nights, and a kitchen where we cook together.\"\n        },\n        {\n            id: 3,\n            title: \"Getting Married\",\n            description: \"The most beautiful day when we promise to love each other forever\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 47,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-pink-400 to-rose-500\",\n            details: \"Our perfect wedding day, surrounded by family and friends, celebrating our eternal love. You in a beautiful white dress, me in a suit, both of us crying happy tears.\"\n        },\n        {\n            id: 4,\n            title: \"Our Little Family\",\n            description: \"Raising beautiful children who have your eyes and my sense of humor\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 55,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-yellow-400 to-orange-500\",\n            details: \"Little ones running around our home, teaching them to be kind and loving like their mama, watching them grow up in a house full of love.\"\n        },\n        {\n            id: 5,\n            title: \"Adventures Together\",\n            description: \"Hiking mountains, beach walks, road trips, and spontaneous adventures\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 63,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-purple-400 to-indigo-500\",\n            details: \"Weekend getaways, hiking trails with breathtaking views, beach sunsets, road trips with our favorite music, and capturing every beautiful moment.\"\n        },\n        {\n            id: 6,\n            title: \"Growing Old Together\",\n            description: \"Still being silly, still in love, with gray hair and wrinkled hands intertwined\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 71,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-red-400 to-pink-500\",\n            details: \"Sitting on our porch at 80, still holding hands, still making each other laugh, still as in love as we are today. Our love story spanning decades.\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"min-h-screen bg-gradient-to-br from-indigo-100 via-purple-100 to-pink-100 py-16 px-4 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                style: {\n                    y,\n                    opacity\n                },\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    ...Array(15)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"absolute text-purple-200 opacity-30\",\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\")\n                        },\n                        animate: {\n                            y: [\n                                0,\n                                -30,\n                                0\n                            ],\n                            rotate: [\n                                0,\n                                360\n                            ],\n                            scale: [\n                                0.8,\n                                1.2,\n                                0.8\n                            ]\n                        },\n                        transition: {\n                            duration: 4 + Math.random() * 3,\n                            repeat: Infinity,\n                            delay: Math.random() * 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            size: 20 + Math.random() * 15,\n                            fill: \"currentColor\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, undefined)\n                    }, i, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600 mb-4\",\n                                children: \"Our Dreams Together\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: \"Every dream I have includes you, Roshni. Here's to our beautiful future together ✨\\uD83D\\uDC95\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-24\",\n                        children: dreams.map((dream, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: index % 2 === 0 ? -100 : 100\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: index * 0.1\n                                },\n                                className: \"flex items-center \".concat(index % 2 === 0 ? 'flex-row' : 'flex-row-reverse', \" gap-8\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 \".concat(index % 2 === 0 ? 'text-right' : 'text-left'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-3xl font-dancing-script text-gray-800 mb-4\",\n                                                    children: dream.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-lg leading-relaxed mb-4\",\n                                                    children: dream.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 leading-relaxed\",\n                                                    children: dream.details\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        whileHover: {\n                                            scale: 1.1,\n                                            rotate: 5\n                                        },\n                                        className: \"w-24 h-24 rounded-full bg-gradient-to-r \".concat(dream.color, \" flex items-center justify-center text-white shadow-xl flex-shrink-0\"),\n                                        children: dream.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, dream.id, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.5\n                        },\n                        className: \"text-center mt-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    animate: {\n                                        scale: [\n                                            1,\n                                            1.05,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    },\n                                    className: \"mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-16 h-16 text-pink-500 mx-auto\",\n                                        fill: \"currentColor\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-4xl font-dancing-script text-gray-800 mb-6\",\n                                    children: \"Forever and Always\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 leading-relaxed mb-8 max-w-3xl mx-auto\",\n                                    children: \"Every single one of these dreams feels possible because I have you by my side, Roshni. You make me believe in forever, in happily ever after, in love that lasts a lifetime. I can't wait to turn every one of these dreams into our beautiful reality.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center space-x-2 mb-6\",\n                                    children: [\n                                        ...Array(9)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4 text-pink-400 animate-pulse\",\n                                            fill: \"currentColor\",\n                                            style: {\n                                                animationDelay: \"\".concat(i * 0.2, \"s\")\n                                            }\n                                        }, i, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-dancing-script text-pink-600\",\n                                    children: \"With all my love, today and always ❤️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-500 mt-2\",\n                                    children: \"- Your Saurabh\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none overflow-hidden\",\n                        children: [\n                            ...Array(10)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                className: \"absolute\",\n                                style: {\n                                    left: \"\".concat(Math.random() * 100, \"%\"),\n                                    top: \"\".concat(Math.random() * 100, \"%\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -100,\n                                        0\n                                    ],\n                                    x: [\n                                        0,\n                                        Math.random() * 50 - 25,\n                                        0\n                                    ],\n                                    rotate: [\n                                        0,\n                                        360\n                                    ],\n                                    scale: [\n                                        0.5,\n                                        1,\n                                        0.5\n                                    ]\n                                },\n                                transition: {\n                                    duration: 6 + Math.random() * 4,\n                                    repeat: Infinity,\n                                    delay: Math.random() * 4\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-pink-300 opacity-40\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 30\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Crown_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 40\n                                        }, undefined)\n                                    ][Math.floor(Math.random() * 3)]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, i, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FutureDreams, \"zrUicr8NahEKota2lIHMY3jqlX0=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform\n    ];\n});\n_c = FutureDreams;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FutureDreams);\nvar _c;\n$RefreshReg$(_c, \"FutureDreams\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FutureDreams.tsx\n"));

/***/ })

});