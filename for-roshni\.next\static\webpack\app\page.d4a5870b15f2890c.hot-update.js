"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/LandingPage.tsx":
/*!****************************************!*\
  !*** ./src/components/LandingPage.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Heart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _ParticleBackground__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ParticleBackground */ \"(app-pages-browser)/./src/components/ParticleBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst LandingPage = ()=>{\n    _s();\n    const [currentText, setCurrentText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showCursor, setShowCursor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasInteracted, setHasInteracted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMainContent, setShowMainContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start start\",\n            \"end start\"\n        ]\n    });\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        -200\n    ]);\n    const opacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n        0,\n        0.5\n    ], [\n        1,\n        0\n    ]);\n    const messages = [\n        \"Hi Roshni... 💖\",\n        \"Happy Birthday, My Love... 🎂\",\n        \"I made this just for you.\",\n        \"A world where only our love exists...\",\n        \"Click anywhere and step into the magic.\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (currentIndex < messages.length) {\n                const message = messages[currentIndex];\n                let charIndex = 0;\n                const typeInterval = setInterval({\n                    \"LandingPage.useEffect.typeInterval\": ()=>{\n                        if (charIndex <= message.length) {\n                            setCurrentText(message.slice(0, charIndex));\n                            charIndex++;\n                        } else {\n                            clearInterval(typeInterval);\n                            setTimeout({\n                                \"LandingPage.useEffect.typeInterval\": ()=>{\n                                    if (currentIndex < messages.length - 1) {\n                                        setCurrentIndex(currentIndex + 1);\n                                        setCurrentText('');\n                                    } else {\n                                        setShowMainContent(true);\n                                    }\n                                }\n                            }[\"LandingPage.useEffect.typeInterval\"], 2000);\n                        }\n                    }\n                }[\"LandingPage.useEffect.typeInterval\"], 80);\n                return ({\n                    \"LandingPage.useEffect\": ()=>clearInterval(typeInterval)\n                })[\"LandingPage.useEffect\"];\n            }\n        }\n    }[\"LandingPage.useEffect\"], [\n        currentIndex\n    ]);\n    const handleInteraction = ()=>{\n        if (!hasInteracted) {\n            setHasInteracted(true);\n        // Trigger audio or other effects here\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            const cursorInterval = setInterval({\n                \"LandingPage.useEffect.cursorInterval\": ()=>{\n                    setShowCursor({\n                        \"LandingPage.useEffect.cursorInterval\": (prev)=>!prev\n                    }[\"LandingPage.useEffect.cursorInterval\"]);\n                }\n            }[\"LandingPage.useEffect.cursorInterval\"], 500);\n            return ({\n                \"LandingPage.useEffect\": ()=>clearInterval(cursorInterval)\n            })[\"LandingPage.useEffect\"];\n        }\n    }[\"LandingPage.useEffect\"], []);\n    const scrollToNext = ()=>{\n        const nextSection = document.getElementById('celebration');\n        if (nextSection) {\n            nextSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"landing\",\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ParticleBackground__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    [\n                        ...Array(25)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            className: \"absolute\",\n                            style: {\n                                left: \"\".concat(Math.random() * 100, \"%\"),\n                                top: \"\".concat(Math.random() * 100, \"%\")\n                            },\n                            animate: {\n                                y: [\n                                    0,\n                                    -50,\n                                    0\n                                ],\n                                x: [\n                                    0,\n                                    Math.random() * 30 - 15,\n                                    0\n                                ],\n                                rotate: [\n                                    0,\n                                    360\n                                ],\n                                scale: [\n                                    0.3,\n                                    1.5,\n                                    0.3\n                                ]\n                            },\n                            transition: {\n                                duration: 4 + Math.random() * 3,\n                                repeat: Infinity,\n                                delay: Math.random() * 3,\n                                ease: \"easeInOut\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-full blur-sm opacity-20\",\n                                style: {\n                                    width: \"\".concat(20 + Math.random() * 40, \"px\"),\n                                    height: \"\".concat(20 + Math.random() * 40, \"px\"),\n                                    background: \"linear-gradient(45deg,\\n                  \".concat([\n                                        '#ff6b9d',\n                                        '#c44569',\n                                        '#f8b500',\n                                        '#ff9ff3',\n                                        '#f368e0'\n                                    ][Math.floor(Math.random() * 5)], \",\\n                  \").concat([\n                                        '#ff6b9d',\n                                        '#c44569',\n                                        '#f8b500',\n                                        '#ff9ff3',\n                                        '#f368e0'\n                                    ][Math.floor(Math.random() * 5)], \"\\n                )\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, undefined)\n                        }, i, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined)),\n                    [\n                        ...Array(8)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            className: \"absolute text-pink-300 opacity-40\",\n                            style: {\n                                left: \"\".concat(Math.random() * 100, \"%\"),\n                                top: \"\".concat(Math.random() * 100, \"%\")\n                            },\n                            animate: {\n                                y: [\n                                    0,\n                                    -40,\n                                    0\n                                ],\n                                rotate: [\n                                    0,\n                                    360\n                                ],\n                                scale: [\n                                    0.8,\n                                    1.3,\n                                    0.8\n                                ]\n                            },\n                            transition: {\n                                duration: 5 + Math.random() * 2,\n                                repeat: Infinity,\n                                delay: Math.random() * 3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                size: 15 + Math.random() * 15,\n                                fill: \"currentColor\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined)\n                        }, \"heart-\".concat(i), false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center z-10 px-4 max-w-5xl mx-auto relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-radial from-pink-200/20 via-purple-200/10 to-transparent rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 1.2,\n                            ease: \"easeOut\"\n                        },\n                        className: \"mb-12 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h1, {\n                                className: \"text-7xl md:text-9xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-pink-500 via-purple-500 to-rose-500 mb-6 leading-tight\",\n                                animate: {\n                                    backgroundPosition: [\n                                        '0% 50%',\n                                        '100% 50%',\n                                        '0% 50%'\n                                    ]\n                                },\n                                transition: {\n                                    duration: 8,\n                                    repeat: Infinity,\n                                    ease: \"linear\"\n                                },\n                                style: {\n                                    backgroundSize: '200% 200%'\n                                },\n                                children: \"Happy Birthday\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    scale: 0.8,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    scale: 1,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 0.5,\n                                    duration: 0.8\n                                },\n                                className: \"relative mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-rose-600 to-pink-600 mb-2\",\n                                        children: \"Roshni Jwala\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        animate: {\n                                            scale: [\n                                                1,\n                                                1.05,\n                                                1\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            repeat: Infinity\n                                        },\n                                        className: \"text-xl md:text-2xl text-gray-600 font-medium\",\n                                        children: \"My Beautiful Babu ✨\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 1,\n                                    duration: 0.8\n                                },\n                                className: \"text-lg md:text-xl text-gray-500 max-w-2xl mx-auto leading-relaxed\",\n                                children: \"This is your magical space. Click. Explore. Feel loved. \\uD83D\\uDC95\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-[120px] flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                delay: 1.5\n                            },\n                            className: \"text-xl md:text-2xl text-gray-600 font-medium\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-block\",\n                                children: [\n                                    currentText,\n                                    showCursor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block w-0.5 h-6 bg-pink-500 ml-1 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined),\n                    currentIndex >= messages.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 2\n                        },\n                        className: \"mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: scrollToNext,\n                            className: \"group flex flex-col items-center text-pink-500 hover:text-pink-600 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"Scroll Down\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            10,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        size: 32,\n                                        className: \"group-hover:scale-110 transition-transform\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: 3\n                },\n                className: \"absolute bottom-8 right-8 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"bg-white/20 backdrop-blur-sm rounded-full p-3 text-pink-500 hover:bg-white/30 transition-all\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 24,\n                        className: \"animate-heartbeat\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\LandingPage.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LandingPage, \"LyT8IY2gNQGSe7Wtskh1kocH5aw=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform\n    ];\n});\n_c = LandingPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LandingPage);\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LandingPage.tsx\n"));

/***/ })

});