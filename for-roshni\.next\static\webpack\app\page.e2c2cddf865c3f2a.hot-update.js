"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/FutureDreams.tsx":
/*!*****************************************!*\
  !*** ./src/components/FutureDreams.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plane.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/baby.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst FutureDreams = ()=>{\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        -100\n    ]);\n    const opacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        0.2,\n        0.8,\n        1\n    ], [\n        0,\n        1,\n        1,\n        0\n    ]);\n    const dreams = [\n        {\n            id: 1,\n            title: \"Travel the World Together\",\n            description: \"Exploring beautiful destinations, creating memories in every corner of the world\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 31,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-blue-400 to-cyan-500\",\n            details: \"Paris for romance, Tokyo for adventure, Maldives for relaxation, and so many more places to discover together, hand in hand.\"\n        },\n        {\n            id: 2,\n            title: \"Our Dream Home\",\n            description: \"A cozy place where we can build our life together, filled with love and laughter\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 39,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-green-400 to-emerald-500\",\n            details: \"A beautiful home with a garden where we can have morning coffee together, a cozy living room for movie nights, and a kitchen where we cook together.\"\n        },\n        {\n            id: 3,\n            title: \"Getting Married\",\n            description: \"The most beautiful day when we promise to love each other forever\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Ring, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 47,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-pink-400 to-rose-500\",\n            details: \"Our perfect wedding day, surrounded by family and friends, celebrating our eternal love. You in a beautiful white dress, me in a suit, both of us crying happy tears.\"\n        },\n        {\n            id: 4,\n            title: \"Our Little Family\",\n            description: \"Raising beautiful children who have your eyes and my sense of humor\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 55,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-yellow-400 to-orange-500\",\n            details: \"Little ones running around our home, teaching them to be kind and loving like their mama, watching them grow up in a house full of love.\"\n        },\n        {\n            id: 5,\n            title: \"Adventures Together\",\n            description: \"Hiking mountains, beach walks, road trips, and spontaneous adventures\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 63,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-purple-400 to-indigo-500\",\n            details: \"Weekend getaways, hiking trails with breathtaking views, beach sunsets, road trips with our favorite music, and capturing every beautiful moment.\"\n        },\n        {\n            id: 6,\n            title: \"Growing Old Together\",\n            description: \"Still being silly, still in love, with gray hair and wrinkled hands intertwined\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 71,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-red-400 to-pink-500\",\n            details: \"Sitting on our porch at 80, still holding hands, still making each other laugh, still as in love as we are today. Our love story spanning decades.\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"min-h-screen bg-gradient-to-br from-indigo-100 via-purple-100 to-pink-100 py-16 px-4 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                style: {\n                    y,\n                    opacity\n                },\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    ...Array(15)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        className: \"absolute text-purple-200 opacity-30\",\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\")\n                        },\n                        animate: {\n                            y: [\n                                0,\n                                -30,\n                                0\n                            ],\n                            rotate: [\n                                0,\n                                360\n                            ],\n                            scale: [\n                                0.8,\n                                1.2,\n                                0.8\n                            ]\n                        },\n                        transition: {\n                            duration: 4 + Math.random() * 3,\n                            repeat: Infinity,\n                            delay: Math.random() * 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: 20 + Math.random() * 15,\n                            fill: \"currentColor\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, undefined)\n                    }, i, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600 mb-4\",\n                                children: \"Our Dreams Together\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: \"Every dream I have includes you, Roshni. Here's to our beautiful future together ✨\\uD83D\\uDC95\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-24\",\n                        children: dreams.map((dream, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: index % 2 === 0 ? -100 : 100\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: index * 0.1\n                                },\n                                className: \"flex items-center \".concat(index % 2 === 0 ? 'flex-row' : 'flex-row-reverse', \" gap-8\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 \".concat(index % 2 === 0 ? 'text-right' : 'text-left'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-3xl font-dancing-script text-gray-800 mb-4\",\n                                                    children: dream.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-lg leading-relaxed mb-4\",\n                                                    children: dream.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 leading-relaxed\",\n                                                    children: dream.details\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        whileHover: {\n                                            scale: 1.1,\n                                            rotate: 5\n                                        },\n                                        className: \"w-24 h-24 rounded-full bg-gradient-to-r \".concat(dream.color, \" flex items-center justify-center text-white shadow-xl flex-shrink-0\"),\n                                        children: dream.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, dream.id, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.5\n                        },\n                        className: \"text-center mt-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    animate: {\n                                        scale: [\n                                            1,\n                                            1.05,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    },\n                                    className: \"mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-16 h-16 text-pink-500 mx-auto\",\n                                        fill: \"currentColor\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-4xl font-dancing-script text-gray-800 mb-6\",\n                                    children: \"Forever and Always\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 leading-relaxed mb-8 max-w-3xl mx-auto\",\n                                    children: \"Every single one of these dreams feels possible because I have you by my side, Roshni. You make me believe in forever, in happily ever after, in love that lasts a lifetime. I can't wait to turn every one of these dreams into our beautiful reality.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center space-x-2 mb-6\",\n                                    children: [\n                                        ...Array(9)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4 text-pink-400 animate-pulse\",\n                                            fill: \"currentColor\",\n                                            style: {\n                                                animationDelay: \"\".concat(i * 0.2, \"s\")\n                                            }\n                                        }, i, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-dancing-script text-pink-600\",\n                                    children: \"With all my love, today and always ❤️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-500 mt-2\",\n                                    children: \"- Your Saurabh\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none overflow-hidden\",\n                        children: [\n                            ...Array(10)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"absolute\",\n                                style: {\n                                    left: \"\".concat(Math.random() * 100, \"%\"),\n                                    top: \"\".concat(Math.random() * 100, \"%\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -100,\n                                        0\n                                    ],\n                                    x: [\n                                        0,\n                                        Math.random() * 50 - 25,\n                                        0\n                                    ],\n                                    rotate: [\n                                        0,\n                                        360\n                                    ],\n                                    scale: [\n                                        0.5,\n                                        1,\n                                        0.5\n                                    ]\n                                },\n                                transition: {\n                                    duration: 6 + Math.random() * 4,\n                                    repeat: Infinity,\n                                    delay: Math.random() * 4\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-pink-300 opacity-40\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 30\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Ring, {}, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 40\n                                        }, undefined)\n                                    ][Math.floor(Math.random() * 3)]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, i, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FutureDreams, \"zrUicr8NahEKota2lIHMY3jqlX0=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform\n    ];\n});\n_c = FutureDreams;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FutureDreams);\nvar _c;\n$RefreshReg$(_c, \"FutureDreams\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FutureDreams.tsx\n"));

/***/ })

});