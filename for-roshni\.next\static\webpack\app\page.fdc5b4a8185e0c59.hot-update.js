/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachToAnimation: () => (/* binding */ attachToAnimation)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/scroll/observe.mjs\");\n/* harmony import */ var _utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/get-timeline.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs\");\n\n\n\nfunction attachToAnimation(animation, options) {\n    const timeline = (0,_utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__.getTimeline)(options);\n    return animation.attachTimeline({\n        timeline: options.target ? undefined : timeline,\n        observe: (valueAnimation) => {\n            valueAnimation.pause();\n            return (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.observeTimeline)((progress) => {\n                valueAnimation.time = valueAnimation.duration * progress;\n            }, timeline);\n        },\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvYXR0YWNoLWFuaW1hdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBQ1U7O0FBRXZEO0FBQ0EscUJBQXFCLG9FQUFXO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDJEQUFlO0FBQ2xDO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVCxLQUFLO0FBQ0w7O0FBRTZCIiwic291cmNlcyI6WyJEOlxcUk9TSE5JXFxmb3JSb3NobmlcXGZvci1yb3NobmlcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcZG9tXFxzY3JvbGxcXGF0dGFjaC1hbmltYXRpb24ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG9ic2VydmVUaW1lbGluZSB9IGZyb20gJ21vdGlvbi1kb20nO1xuaW1wb3J0IHsgZ2V0VGltZWxpbmUgfSBmcm9tICcuL3V0aWxzL2dldC10aW1lbGluZS5tanMnO1xuXG5mdW5jdGlvbiBhdHRhY2hUb0FuaW1hdGlvbihhbmltYXRpb24sIG9wdGlvbnMpIHtcbiAgICBjb25zdCB0aW1lbGluZSA9IGdldFRpbWVsaW5lKG9wdGlvbnMpO1xuICAgIHJldHVybiBhbmltYXRpb24uYXR0YWNoVGltZWxpbmUoe1xuICAgICAgICB0aW1lbGluZTogb3B0aW9ucy50YXJnZXQgPyB1bmRlZmluZWQgOiB0aW1lbGluZSxcbiAgICAgICAgb2JzZXJ2ZTogKHZhbHVlQW5pbWF0aW9uKSA9PiB7XG4gICAgICAgICAgICB2YWx1ZUFuaW1hdGlvbi5wYXVzZSgpO1xuICAgICAgICAgICAgcmV0dXJuIG9ic2VydmVUaW1lbGluZSgocHJvZ3Jlc3MpID0+IHtcbiAgICAgICAgICAgICAgICB2YWx1ZUFuaW1hdGlvbi50aW1lID0gdmFsdWVBbmltYXRpb24uZHVyYXRpb24gKiBwcm9ncmVzcztcbiAgICAgICAgICAgIH0sIHRpbWVsaW5lKTtcbiAgICAgICAgfSxcbiAgICB9KTtcbn1cblxuZXhwb3J0IHsgYXR0YWNoVG9BbmltYXRpb24gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachToFunction: () => (/* binding */ attachToFunction)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/scroll/observe.mjs\");\n/* harmony import */ var _track_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./track.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\");\n/* harmony import */ var _utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/get-timeline.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs\");\n\n\n\n\n/**\n * If the onScroll function has two arguments, it's expecting\n * more specific information about the scroll from scrollInfo.\n */\nfunction isOnScrollWithInfo(onScroll) {\n    return onScroll.length === 2;\n}\nfunction attachToFunction(onScroll, options) {\n    if (isOnScrollWithInfo(onScroll)) {\n        return (0,_track_mjs__WEBPACK_IMPORTED_MODULE_0__.scrollInfo)((info) => {\n            onScroll(info[options.axis].progress, info);\n        }, options);\n    }\n    else {\n        return (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.observeTimeline)(onScroll, (0,_utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_2__.getTimeline)(options));\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvYXR0YWNoLWZ1bmN0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZDO0FBQ0o7QUFDYzs7QUFFdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzREFBVTtBQUN6QjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZUFBZSwyREFBZSxXQUFXLG9FQUFXO0FBQ3BEO0FBQ0E7O0FBRTRCIiwic291cmNlcyI6WyJEOlxcUk9TSE5JXFxmb3JSb3NobmlcXGZvci1yb3NobmlcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcZG9tXFxzY3JvbGxcXGF0dGFjaC1mdW5jdGlvbi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgb2JzZXJ2ZVRpbWVsaW5lIH0gZnJvbSAnbW90aW9uLWRvbSc7XG5pbXBvcnQgeyBzY3JvbGxJbmZvIH0gZnJvbSAnLi90cmFjay5tanMnO1xuaW1wb3J0IHsgZ2V0VGltZWxpbmUgfSBmcm9tICcuL3V0aWxzL2dldC10aW1lbGluZS5tanMnO1xuXG4vKipcbiAqIElmIHRoZSBvblNjcm9sbCBmdW5jdGlvbiBoYXMgdHdvIGFyZ3VtZW50cywgaXQncyBleHBlY3RpbmdcbiAqIG1vcmUgc3BlY2lmaWMgaW5mb3JtYXRpb24gYWJvdXQgdGhlIHNjcm9sbCBmcm9tIHNjcm9sbEluZm8uXG4gKi9cbmZ1bmN0aW9uIGlzT25TY3JvbGxXaXRoSW5mbyhvblNjcm9sbCkge1xuICAgIHJldHVybiBvblNjcm9sbC5sZW5ndGggPT09IDI7XG59XG5mdW5jdGlvbiBhdHRhY2hUb0Z1bmN0aW9uKG9uU2Nyb2xsLCBvcHRpb25zKSB7XG4gICAgaWYgKGlzT25TY3JvbGxXaXRoSW5mbyhvblNjcm9sbCkpIHtcbiAgICAgICAgcmV0dXJuIHNjcm9sbEluZm8oKGluZm8pID0+IHtcbiAgICAgICAgICAgIG9uU2Nyb2xsKGluZm9bb3B0aW9ucy5heGlzXS5wcm9ncmVzcywgaW5mbyk7XG4gICAgICAgIH0sIG9wdGlvbnMpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIG9ic2VydmVUaW1lbGluZShvblNjcm9sbCwgZ2V0VGltZWxpbmUob3B0aW9ucykpO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgYXR0YWNoVG9GdW5jdGlvbiB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scroll: () => (/* binding */ scroll)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/noop.mjs\");\n/* harmony import */ var _attach_animation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./attach-animation.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs\");\n/* harmony import */ var _attach_function_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./attach-function.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs\");\n\n\n\n\nfunction scroll(onScroll, { axis = \"y\", container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n    const optionsWithDefaults = { axis, container, ...options };\n    return typeof onScroll === \"function\"\n        ? (0,_attach_function_mjs__WEBPACK_IMPORTED_MODULE_1__.attachToFunction)(onScroll, optionsWithDefaults)\n        : (0,_attach_animation_mjs__WEBPACK_IMPORTED_MODULE_2__.attachToAnimation)(onScroll, optionsWithDefaults);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0M7QUFDdUI7QUFDRjs7QUFFekQsNEJBQTRCLGdFQUFnRSxJQUFJO0FBQ2hHO0FBQ0EsZUFBZSw4Q0FBSTtBQUNuQixrQ0FBa0M7QUFDbEM7QUFDQSxVQUFVLHNFQUFnQjtBQUMxQixVQUFVLHdFQUFpQjtBQUMzQjs7QUFFa0IiLCJzb3VyY2VzIjpbIkQ6XFxST1NITklcXGZvclJvc2huaVxcZm9yLXJvc2huaVxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xccmVuZGVyXFxkb21cXHNjcm9sbFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG5vb3AgfSBmcm9tICdtb3Rpb24tdXRpbHMnO1xuaW1wb3J0IHsgYXR0YWNoVG9BbmltYXRpb24gfSBmcm9tICcuL2F0dGFjaC1hbmltYXRpb24ubWpzJztcbmltcG9ydCB7IGF0dGFjaFRvRnVuY3Rpb24gfSBmcm9tICcuL2F0dGFjaC1mdW5jdGlvbi5tanMnO1xuXG5mdW5jdGlvbiBzY3JvbGwob25TY3JvbGwsIHsgYXhpcyA9IFwieVwiLCBjb250YWluZXIgPSBkb2N1bWVudC5zY3JvbGxpbmdFbGVtZW50LCAuLi5vcHRpb25zIH0gPSB7fSkge1xuICAgIGlmICghY29udGFpbmVyKVxuICAgICAgICByZXR1cm4gbm9vcDtcbiAgICBjb25zdCBvcHRpb25zV2l0aERlZmF1bHRzID0geyBheGlzLCBjb250YWluZXIsIC4uLm9wdGlvbnMgfTtcbiAgICByZXR1cm4gdHlwZW9mIG9uU2Nyb2xsID09PSBcImZ1bmN0aW9uXCJcbiAgICAgICAgPyBhdHRhY2hUb0Z1bmN0aW9uKG9uU2Nyb2xsLCBvcHRpb25zV2l0aERlZmF1bHRzKVxuICAgICAgICA6IGF0dGFjaFRvQW5pbWF0aW9uKG9uU2Nyb2xsLCBvcHRpb25zV2l0aERlZmF1bHRzKTtcbn1cblxuZXhwb3J0IHsgc2Nyb2xsIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createScrollInfo: () => (/* binding */ createScrollInfo),\n/* harmony export */   updateScrollInfo: () => (/* binding */ updateScrollInfo)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/progress.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/velocity-per-second.mjs\");\n\n\n/**\n * A time in milliseconds, beyond which we consider the scroll velocity to be 0.\n */\nconst maxElapsed = 50;\nconst createAxisInfo = () => ({\n    current: 0,\n    offset: [],\n    progress: 0,\n    scrollLength: 0,\n    targetOffset: 0,\n    targetLength: 0,\n    containerLength: 0,\n    velocity: 0,\n});\nconst createScrollInfo = () => ({\n    time: 0,\n    x: createAxisInfo(),\n    y: createAxisInfo(),\n});\nconst keys = {\n    x: {\n        length: \"Width\",\n        position: \"Left\",\n    },\n    y: {\n        length: \"Height\",\n        position: \"Top\",\n    },\n};\nfunction updateAxisInfo(element, axisName, info, time) {\n    const axis = info[axisName];\n    const { length, position } = keys[axisName];\n    const prev = axis.current;\n    const prevTime = info.time;\n    axis.current = element[`scroll${position}`];\n    axis.scrollLength = element[`scroll${length}`] - element[`client${length}`];\n    axis.offset.length = 0;\n    axis.offset[0] = 0;\n    axis.offset[1] = axis.scrollLength;\n    axis.progress = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.progress)(0, axis.scrollLength, axis.current);\n    const elapsed = time - prevTime;\n    axis.velocity =\n        elapsed > maxElapsed\n            ? 0\n            : (0,motion_utils__WEBPACK_IMPORTED_MODULE_1__.velocityPerSecond)(axis.current - prev, elapsed);\n}\nfunction updateScrollInfo(element, info, time) {\n    updateAxisInfo(element, \"x\", info, time);\n    updateAxisInfo(element, \"y\", info, time);\n    info.time = time;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   namedEdges: () => (/* binding */ namedEdges),\n/* harmony export */   resolveEdge: () => (/* binding */ resolveEdge)\n/* harmony export */ });\nconst namedEdges = {\n    start: 0,\n    center: 0.5,\n    end: 1,\n};\nfunction resolveEdge(edge, length, inset = 0) {\n    let delta = 0;\n    /**\n     * If we have this edge defined as a preset, replace the definition\n     * with the numerical value.\n     */\n    if (edge in namedEdges) {\n        edge = namedEdges[edge];\n    }\n    /**\n     * Handle unit values\n     */\n    if (typeof edge === \"string\") {\n        const asNumber = parseFloat(edge);\n        if (edge.endsWith(\"px\")) {\n            delta = asNumber;\n        }\n        else if (edge.endsWith(\"%\")) {\n            edge = asNumber / 100;\n        }\n        else if (edge.endsWith(\"vw\")) {\n            delta = (asNumber / 100) * document.documentElement.clientWidth;\n        }\n        else if (edge.endsWith(\"vh\")) {\n            delta = (asNumber / 100) * document.documentElement.clientHeight;\n        }\n        else {\n            edge = asNumber;\n        }\n    }\n    /**\n     * If the edge is defined as a number, handle as a progress value.\n     */\n    if (typeof edge === \"number\") {\n        delta = length * edge;\n    }\n    return inset + delta;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffsets: () => (/* binding */ resolveOffsets)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/interpolate.mjs\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/keyframes/offsets/default.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/clamp.mjs\");\n/* harmony import */ var _inset_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./inset.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\");\n/* harmony import */ var _offset_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offset.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\");\n/* harmony import */ var _presets_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./presets.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\");\n\n\n\n\n\n\nconst point = { x: 0, y: 0 };\nfunction getTargetSize(target) {\n    return \"getBBox\" in target && target.tagName !== \"svg\"\n        ? target.getBBox()\n        : { width: target.clientWidth, height: target.clientHeight };\n}\nfunction resolveOffsets(container, info, options) {\n    const { offset: offsetDefinition = _presets_mjs__WEBPACK_IMPORTED_MODULE_0__.ScrollOffset.All } = options;\n    const { target = container, axis = \"y\" } = options;\n    const lengthLabel = axis === \"y\" ? \"height\" : \"width\";\n    const inset = target !== container ? (0,_inset_mjs__WEBPACK_IMPORTED_MODULE_1__.calcInset)(target, container) : point;\n    /**\n     * Measure the target and container. If they're the same thing then we\n     * use the container's scrollWidth/Height as the target, from there\n     * all other calculations can remain the same.\n     */\n    const targetSize = target === container\n        ? { width: container.scrollWidth, height: container.scrollHeight }\n        : getTargetSize(target);\n    const containerSize = {\n        width: container.clientWidth,\n        height: container.clientHeight,\n    };\n    /**\n     * Reset the length of the resolved offset array rather than creating a new one.\n     * TODO: More reusable data structures for targetSize/containerSize would also be good.\n     */\n    info[axis].offset.length = 0;\n    /**\n     * Populate the offset array by resolving the user's offset definition into\n     * a list of pixel scroll offets.\n     */\n    let hasChanged = !info[axis].interpolate;\n    const numOffsets = offsetDefinition.length;\n    for (let i = 0; i < numOffsets; i++) {\n        const offset = (0,_offset_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffset)(offsetDefinition[i], containerSize[lengthLabel], targetSize[lengthLabel], inset[axis]);\n        if (!hasChanged && offset !== info[axis].interpolatorOffsets[i]) {\n            hasChanged = true;\n        }\n        info[axis].offset[i] = offset;\n    }\n    /**\n     * If the pixel scroll offsets have changed, create a new interpolator function\n     * to map scroll value into a progress.\n     */\n    if (hasChanged) {\n        info[axis].interpolate = (0,motion_dom__WEBPACK_IMPORTED_MODULE_3__.interpolate)(info[axis].offset, (0,motion_dom__WEBPACK_IMPORTED_MODULE_4__.defaultOffset)(offsetDefinition), { clamp: false });\n        info[axis].interpolatorOffsets = [...info[axis].offset];\n    }\n    info[axis].progress = (0,motion_utils__WEBPACK_IMPORTED_MODULE_5__.clamp)(0, 1, info[axis].interpolate(info[axis].current));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcInset: () => (/* binding */ calcInset)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-html-element.mjs\");\n\n\nfunction calcInset(element, container) {\n    const inset = { x: 0, y: 0 };\n    let current = element;\n    while (current && current !== container) {\n        if ((0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(current)) {\n            inset.x += current.offsetLeft;\n            inset.y += current.offsetTop;\n            current = current.offsetParent;\n        }\n        else if (current.tagName === \"svg\") {\n            /**\n             * This isn't an ideal approach to measuring the offset of <svg /> tags.\n             * It would be preferable, given they behave like HTMLElements in most ways\n             * to use offsetLeft/Top. But these don't exist on <svg />. Likewise we\n             * can't use .getBBox() like most SVG elements as these provide the offset\n             * relative to the SVG itself, which for <svg /> is usually 0x0.\n             */\n            const svgBoundingBox = current.getBoundingClientRect();\n            current = current.parentElement;\n            const parentBoundingBox = current.getBoundingClientRect();\n            inset.x += svgBoundingBox.left - parentBoundingBox.left;\n            inset.y += svgBoundingBox.top - parentBoundingBox.top;\n        }\n        else if (current instanceof SVGGraphicsElement) {\n            const { x, y } = current.getBBox();\n            inset.x += x;\n            inset.y += y;\n            let svg = null;\n            let parent = current.parentNode;\n            while (!svg) {\n                if (parent.tagName === \"svg\") {\n                    svg = parent;\n                }\n                parent = current.parentNode;\n            }\n            current = svg;\n        }\n        else {\n            break;\n        }\n    }\n    return inset;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffset: () => (/* binding */ resolveOffset)\n/* harmony export */ });\n/* harmony import */ var _edge_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edge.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\");\n\n\nconst defaultOffset = [0, 0];\nfunction resolveOffset(offset, containerLength, targetLength, targetInset) {\n    let offsetDefinition = Array.isArray(offset) ? offset : defaultOffset;\n    let targetPoint = 0;\n    let containerPoint = 0;\n    if (typeof offset === \"number\") {\n        /**\n         * If we're provided offset: [0, 0.5, 1] then each number x should become\n         * [x, x], so we default to the behaviour of mapping 0 => 0 of both target\n         * and container etc.\n         */\n        offsetDefinition = [offset, offset];\n    }\n    else if (typeof offset === \"string\") {\n        offset = offset.trim();\n        if (offset.includes(\" \")) {\n            offsetDefinition = offset.split(\" \");\n        }\n        else {\n            /**\n             * If we're provided a definition like \"100px\" then we want to apply\n             * that only to the top of the target point, leaving the container at 0.\n             * Whereas a named offset like \"end\" should be applied to both.\n             */\n            offsetDefinition = [offset, _edge_mjs__WEBPACK_IMPORTED_MODULE_0__.namedEdges[offset] ? offset : `0`];\n        }\n    }\n    targetPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[0], targetLength, targetInset);\n    containerPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[1], containerLength);\n    return targetPoint - containerPoint;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollOffset: () => (/* binding */ ScrollOffset)\n/* harmony export */ });\nconst ScrollOffset = {\n    Enter: [\n        [0, 1],\n        [1, 1],\n    ],\n    Exit: [\n        [0, 0],\n        [1, 0],\n    ],\n    Any: [\n        [1, 0],\n        [0, 1],\n    ],\n    All: [\n        [0, 0],\n        [1, 1],\n    ],\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvb2Zmc2V0cy9wcmVzZXRzLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsiRDpcXFJPU0hOSVxcZm9yUm9zaG5pXFxmb3Itcm9zaG5pXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGRvbVxcc2Nyb2xsXFxvZmZzZXRzXFxwcmVzZXRzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBTY3JvbGxPZmZzZXQgPSB7XG4gICAgRW50ZXI6IFtcbiAgICAgICAgWzAsIDFdLFxuICAgICAgICBbMSwgMV0sXG4gICAgXSxcbiAgICBFeGl0OiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDBdLFxuICAgIF0sXG4gICAgQW55OiBbXG4gICAgICAgIFsxLCAwXSxcbiAgICAgICAgWzAsIDFdLFxuICAgIF0sXG4gICAgQWxsOiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDFdLFxuICAgIF0sXG59O1xuXG5leHBvcnQgeyBTY3JvbGxPZmZzZXQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createOnScrollHandler: () => (/* binding */ createOnScrollHandler)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/warn-once.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offsets/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\");\n\n\n\n\nfunction measure(container, target = container, info) {\n    /**\n     * Find inset of target within scrollable container\n     */\n    info.x.targetOffset = 0;\n    info.y.targetOffset = 0;\n    if (target !== container) {\n        let node = target;\n        while (node && node !== container) {\n            info.x.targetOffset += node.offsetLeft;\n            info.y.targetOffset += node.offsetTop;\n            node = node.offsetParent;\n        }\n    }\n    info.x.targetLength =\n        target === container ? target.scrollWidth : target.clientWidth;\n    info.y.targetLength =\n        target === container ? target.scrollHeight : target.clientHeight;\n    info.x.containerLength = container.clientWidth;\n    info.y.containerLength = container.clientHeight;\n    /**\n     * In development mode ensure scroll containers aren't position: static as this makes\n     * it difficult to measure their relative positions.\n     */\n    if (true) {\n        if (container && target && target !== container) {\n            (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.warnOnce)(getComputedStyle(container).position !== \"static\", \"Please ensure that the container has a non-static position, like 'relative', 'fixed', or 'absolute' to ensure scroll offset is calculated correctly.\");\n        }\n    }\n}\nfunction createOnScrollHandler(element, onScroll, info, options = {}) {\n    return {\n        measure: (time) => {\n            measure(element, options.target, info);\n            (0,_info_mjs__WEBPACK_IMPORTED_MODULE_1__.updateScrollInfo)(element, info, time);\n            if (options.offset || options.target) {\n                (0,_offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffsets)(element, info, options);\n            }\n        },\n        notify: () => onScroll(info),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scrollInfo: () => (/* binding */ scrollInfo)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/noop.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./on-scroll-handler.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\");\n\n\n\n\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.scrollingElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = (0,_info_mjs__WEBPACK_IMPORTED_MODULE_1__.createScrollInfo)();\n    const containerHandler = (0,_on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_2__.createOnScrollHandler)(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers) {\n                handler.measure(motion_dom__WEBPACK_IMPORTED_MODULE_3__.frameData.timestamp);\n            }\n            motion_dom__WEBPACK_IMPORTED_MODULE_3__.frame.preUpdate(notifyAll);\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers) {\n                handler.notify();\n            }\n        };\n        const listener = () => motion_dom__WEBPACK_IMPORTED_MODULE_3__.frame.read(measureAll);\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, (0,motion_dom__WEBPACK_IMPORTED_MODULE_4__.resize)(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n        listener();\n    }\n    const listener = scrollListeners.get(container);\n    motion_dom__WEBPACK_IMPORTED_MODULE_3__.frame.read(listener, false, true);\n    return () => {\n        (0,motion_dom__WEBPACK_IMPORTED_MODULE_3__.cancelFrame)(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            resizeListeners.get(container)?.();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTimeline: () => (/* binding */ getTimeline)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs\");\n/* harmony import */ var _track_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../track.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\");\n\n\n\nconst timelineCache = new Map();\nfunction scrollTimelineFallback(options) {\n    const currentTime = { value: 0 };\n    const cancel = (0,_track_mjs__WEBPACK_IMPORTED_MODULE_0__.scrollInfo)((info) => {\n        currentTime.value = info[options.axis].progress * 100;\n    }, options);\n    return { currentTime, cancel };\n}\nfunction getTimeline({ source, container, ...options }) {\n    const { axis } = options;\n    if (source)\n        container = source;\n    const containerCache = timelineCache.get(container) ?? new Map();\n    timelineCache.set(container, containerCache);\n    const targetKey = options.target ?? \"self\";\n    const targetCache = containerCache.get(targetKey) ?? {};\n    const axisKey = axis + (options.offset ?? []).join(\",\");\n    if (!targetCache[axisKey]) {\n        targetCache[axisKey] =\n            !options.target && (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.supportsScrollTimeline)()\n                ? new ScrollTimeline({ source: container, axis })\n                : scrollTimelineFallback({ container, ...options });\n    }\n    return targetCache[axisKey];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-combine-values.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCombineMotionValues: () => (/* binding */ useCombineMotionValues)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n\n\n\n\nfunction useCombineMotionValues(values, combineValues) {\n    /**\n     * Initialise the returned motion value. This remains the same between renders.\n     */\n    const value = (0,_use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.useMotionValue)(combineValues());\n    /**\n     * Create a function that will update the template motion value with the latest values.\n     * This is pre-bound so whenever a motion value updates it can schedule its\n     * execution in Framesync. If it's already been scheduled it won't be fired twice\n     * in a single frame.\n     */\n    const updateValue = () => value.set(combineValues());\n    /**\n     * Synchronously update the motion value with the latest values during the render.\n     * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n     */\n    updateValue();\n    /**\n     * Subscribe to all motion values found within the template. Whenever any of them change,\n     * schedule an update.\n     */\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__.useIsomorphicLayoutEffect)(() => {\n        const scheduleUpdate = () => motion_dom__WEBPACK_IMPORTED_MODULE_2__.frame.preRender(updateValue, false, true);\n        const subscriptions = values.map((v) => v.on(\"change\", scheduleUpdate));\n        return () => {\n            subscriptions.forEach((unsubscribe) => unsubscribe());\n            (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(updateValue);\n        };\n    });\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-computed.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useComputed: () => (/* binding */ useComputed)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n\n\n\nfunction useComputed(compute) {\n    /**\n     * Open session of collectMotionValues. Any MotionValue that calls get()\n     * will be saved into this array.\n     */\n    motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = [];\n    compute();\n    const value = (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__.useCombineMotionValues)(motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current, compute);\n    /**\n     * Synchronously close session of collectMotionValues.\n     */\n    motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = undefined;\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLWNvbXB1dGVkLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUQ7QUFDaUI7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJEQUFtQjtBQUN2QjtBQUNBLGtCQUFrQiwrRUFBc0IsQ0FBQywyREFBbUI7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyREFBbUI7QUFDdkI7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIkQ6XFxST1NITklcXGZvclJvc2huaVxcZm9yLXJvc2huaVxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcdmFsdWVcXHVzZS1jb21wdXRlZC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29sbGVjdE1vdGlvblZhbHVlcyB9IGZyb20gJ21vdGlvbi1kb20nO1xuaW1wb3J0IHsgdXNlQ29tYmluZU1vdGlvblZhbHVlcyB9IGZyb20gJy4vdXNlLWNvbWJpbmUtdmFsdWVzLm1qcyc7XG5cbmZ1bmN0aW9uIHVzZUNvbXB1dGVkKGNvbXB1dGUpIHtcbiAgICAvKipcbiAgICAgKiBPcGVuIHNlc3Npb24gb2YgY29sbGVjdE1vdGlvblZhbHVlcy4gQW55IE1vdGlvblZhbHVlIHRoYXQgY2FsbHMgZ2V0KClcbiAgICAgKiB3aWxsIGJlIHNhdmVkIGludG8gdGhpcyBhcnJheS5cbiAgICAgKi9cbiAgICBjb2xsZWN0TW90aW9uVmFsdWVzLmN1cnJlbnQgPSBbXTtcbiAgICBjb21wdXRlKCk7XG4gICAgY29uc3QgdmFsdWUgPSB1c2VDb21iaW5lTW90aW9uVmFsdWVzKGNvbGxlY3RNb3Rpb25WYWx1ZXMuY3VycmVudCwgY29tcHV0ZSk7XG4gICAgLyoqXG4gICAgICogU3luY2hyb25vdXNseSBjbG9zZSBzZXNzaW9uIG9mIGNvbGxlY3RNb3Rpb25WYWx1ZXMuXG4gICAgICovXG4gICAgY29sbGVjdE1vdGlvblZhbHVlcy5jdXJyZW50ID0gdW5kZWZpbmVkO1xuICAgIHJldHVybiB2YWx1ZTtcbn1cblxuZXhwb3J0IHsgdXNlQ29tcHV0ZWQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-motion-value.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMotionValue: () => (/* binding */ useMotionValue)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n\n\n\n\n\n/**\n * Creates a `MotionValue` to track the state and velocity of a value.\n *\n * Usually, these are created automatically. For advanced use-cases, like use with `useTransform`, you can create `MotionValue`s externally and pass them into the animated component via the `style` prop.\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const scale = useMotionValue(1)\n *\n *   return <motion.div style={{ scale }} />\n * }\n * ```\n *\n * @param initial - The initial state.\n *\n * @public\n */\nfunction useMotionValue(initial) {\n    const value = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__.useConstant)(() => (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(initial));\n    /**\n     * If this motion value is being used in static mode, like on\n     * the Framer canvas, force components to rerender when the motion\n     * value is updated.\n     */\n    const { isStatic } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext);\n    if (isStatic) {\n        const [, setLatest] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initial);\n        (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => value.on(\"change\", setLatest), []);\n    }\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-scroll.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useScroll: () => (/* binding */ useScroll)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/errors.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _render_dom_scroll_index_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../render/dom/scroll/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n\n\n\n\n\n\n\nfunction refWarning(name, ref) {\n    (0,motion_utils__WEBPACK_IMPORTED_MODULE_1__.warning)(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n    scrollX: (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollY: (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollXProgress: (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollYProgress: (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n});\nfunction useScroll({ container, target, layoutEffect = true, ...options } = {}) {\n    const values = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__.useConstant)(createScrollMotionValues);\n    const useLifecycleEffect = layoutEffect\n        ? _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_4__.useIsomorphicLayoutEffect\n        : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n    useLifecycleEffect(() => {\n        refWarning(\"target\", target);\n        refWarning(\"container\", container);\n        return (0,_render_dom_scroll_index_mjs__WEBPACK_IMPORTED_MODULE_5__.scroll)((_progress, { x, y, }) => {\n            values.scrollX.set(x.current);\n            values.scrollXProgress.set(x.progress);\n            values.scrollY.set(y.current);\n            values.scrollYProgress.set(y.progress);\n        }, {\n            ...options,\n            container: container?.current || undefined,\n            target: target?.current || undefined,\n        });\n    }, [container, target, JSON.stringify(options.offset)]);\n    return values;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-transform.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTransform: () => (/* binding */ useTransform)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n/* harmony import */ var _use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-computed.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\");\n\n\n\n\n\nfunction useTransform(input, inputRangeOrTransformer, outputRange, options) {\n    if (typeof input === \"function\") {\n        return (0,_use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__.useComputed)(input);\n    }\n    const transformer = typeof inputRangeOrTransformer === \"function\"\n        ? inputRangeOrTransformer\n        : (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.transform)(inputRangeOrTransformer, outputRange, options);\n    return Array.isArray(input)\n        ? useListTransform(input, transformer)\n        : useListTransform([input], ([latest]) => transformer(latest));\n}\nfunction useListTransform(values, transformer) {\n    const latest = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant)(() => []);\n    return (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__.useCombineMotionValues)(values, () => {\n        latest.length = 0;\n        const numValues = values.length;\n        for (let i = 0; i < numValues; i++) {\n            latest[i] = values[i].get();\n        }\n        return transformer(latest);\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/baby.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/baby.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Baby)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M10 16c.5.3 1.2.5 2 .5s1.5-.2 2-.5\",\n            key: \"1u7htd\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M15 12h.01\",\n            key: \"1k8ypt\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19.38 6.813A9 9 0 0 1 20.8 10.2a2 2 0 0 1 0 3.6 9 9 0 0 1-17.6 0 2 2 0 0 1 0-3.6A9 9 0 0 1 12 3c2 0 3.5 1.1 3.5 2.5s-.9 2.5-2 2.5c-.8 0-1.5-.4-1.5-1\",\n            key: \"11xh7x\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 12h.01\",\n            key: \"157uk2\"\n        }\n    ]\n];\nconst Baby = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"baby\", __iconNode);\n //# sourceMappingURL=baby.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvYmFieS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHTyxNQUFNLFVBQXVCO0lBQ2xDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFzQztZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7SUFDbkU7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQWM7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQzNDO1FBQ0U7UUFDQTtZQUNFLENBQUc7WUFDSCxHQUFLO1FBQUE7S0FFVDtJQUNBO1FBQUMsQ0FBUTtRQUFBLENBQUU7WUFBQSxFQUFHLFlBQWE7WUFBQSxJQUFLO1FBQVU7S0FBQTtDQUM1QztBQWFNLFdBQU8sa0VBQWlCLFNBQVEsQ0FBVSIsInNvdXJjZXMiOlsiRDpcXHNyY1xcaWNvbnNcXGJhYnkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1xuICBbJ3BhdGgnLCB7IGQ6ICdNMTAgMTZjLjUuMyAxLjIuNSAyIC41czEuNS0uMiAyLS41Jywga2V5OiAnMXU3aHRkJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTE1IDEyaC4wMScsIGtleTogJzFrOHlwdCcgfV0sXG4gIFtcbiAgICAncGF0aCcsXG4gICAge1xuICAgICAgZDogJ00xOS4zOCA2LjgxM0E5IDkgMCAwIDEgMjAuOCAxMC4yYTIgMiAwIDAgMSAwIDMuNiA5IDkgMCAwIDEtMTcuNiAwIDIgMiAwIDAgMSAwLTMuNkE5IDkgMCAwIDEgMTIgM2MyIDAgMy41IDEuMSAzLjUgMi41cy0uOSAyLjUtMiAyLjVjLS44IDAtMS41LS40LTEuNS0xJyxcbiAgICAgIGtleTogJzExeGg3eCcsXG4gICAgfSxcbiAgXSxcbiAgWydwYXRoJywgeyBkOiAnTTkgMTJoLjAxJywga2V5OiAnMTU3dWsyJyB9XSxcbl07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBCYWJ5XG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NVEFnTVRaakxqVXVNeUF4TGpJdU5TQXlJQzQxY3pFdU5TMHVNaUF5TFM0MUlpQXZQZ29nSUR4d1lYUm9JR1E5SWsweE5TQXhNbWd1TURFaUlDOCtDaUFnUEhCaGRHZ2daRDBpVFRFNUxqTTRJRFl1T0RFelFUa2dPU0F3SURBZ01TQXlNQzQ0SURFd0xqSmhNaUF5SURBZ01DQXhJREFnTXk0MklEa2dPU0F3SURBZ01TMHhOeTQySURBZ01pQXlJREFnTUNBeElEQXRNeTQyUVRrZ09TQXdJREFnTVNBeE1pQXpZeklnTUNBekxqVWdNUzR4SURNdU5TQXlMalZ6TFM0NUlESXVOUzB5SURJdU5XTXRMamdnTUMweExqVXRMalF0TVM0MUxURWlJQzgrQ2lBZ1BIQmhkR2dnWkQwaVRUa2dNVEpvTGpBeElpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2JhYnlcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBCYWJ5ID0gY3JlYXRlTHVjaWRlSWNvbignYmFieScsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBCYWJ5O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/baby.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/house.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ House)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\",\n            key: \"5wwlr5\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\",\n            key: \"1d0kgt\"\n        }\n    ]\n];\nconst House = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"house\", __iconNode);\n //# sourceMappingURL=house.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/pause.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Pause)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            x: \"14\",\n            y: \"4\",\n            width: \"4\",\n            height: \"16\",\n            rx: \"1\",\n            key: \"zuxfzm\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            x: \"6\",\n            y: \"4\",\n            width: \"4\",\n            height: \"16\",\n            rx: \"1\",\n            key: \"1okwgv\"\n        }\n    ]\n];\nconst Pause = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"pause\", __iconNode);\n //# sourceMappingURL=pause.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plane.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/plane.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Plane)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z\",\n            key: \"1v9wt8\"\n        }\n    ]\n];\nconst Plane = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"plane\", __iconNode);\n //# sourceMappingURL=plane.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plane.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/play.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Play)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"polygon\",\n        {\n            points: \"6 3 20 12 6 21 6 3\",\n            key: \"1oa8hb\"\n        }\n    ]\n];\nconst Play = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"play\", __iconNode);\n //# sourceMappingURL=play.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvcGxheS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHYSxtQkFBdUI7SUFBQztRQUFDLFNBQVc7UUFBQTtZQUFFLFFBQVEsb0JBQXNCO1lBQUEsSUFBSyxTQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhM0YsV0FBTyxrRUFBaUIsU0FBUSxDQUFVIiwic291cmNlcyI6WyJEOlxcc3JjXFxpY29uc1xccGxheS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydwb2x5Z29uJywgeyBwb2ludHM6ICc2IDMgMjAgMTIgNiAyMSA2IDMnLCBrZXk6ICcxb2E4aGInIH1dXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIFBsYXlcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHOXNlV2R2YmlCd2IybHVkSE05SWpZZ015QXlNQ0F4TWlBMklESXhJRFlnTXlJZ0x6NEtQQzl6ZG1jK0NnPT0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL3BsYXlcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBQbGF5ID0gY3JlYXRlTHVjaWRlSWNvbigncGxheScsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBQbGF5O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/volume-2.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Volume2)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z\",\n            key: \"uqj9uw\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 9a5 5 0 0 1 0 6\",\n            key: \"1q6k2b\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19.364 18.364a9 9 0 0 0 0-12.728\",\n            key: \"ijwkga\"\n        }\n    ]\n];\nconst Volume2 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"volume-2\", __iconNode);\n //# sourceMappingURL=volume-2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/volume-x.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ VolumeX)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z\",\n            key: \"uqj9uw\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"22\",\n            x2: \"16\",\n            y1: \"9\",\n            y2: \"15\",\n            key: \"1ewh16\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"16\",\n            x2: \"22\",\n            y1: \"9\",\n            y2: \"15\",\n            key: \"5ykzw1\"\n        }\n    ]\n];\nconst VolumeX = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"volume-x\", __iconNode);\n //# sourceMappingURL=volume-x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js":
/*!*******************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/x.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ X)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M18 6 6 18\",\n            key: \"1bl5f8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m6 6 12 12\",\n            key: \"d8bk6v\"\n        }\n    ]\n];\nconst X = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"x\", __iconNode);\n //# sourceMappingURL=x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMveC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHTyxNQUFNLFVBQXVCO0lBQ2xDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFjO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUMzQztRQUFDLENBQVE7UUFBQSxDQUFFO1lBQUEsRUFBRyxhQUFjO1lBQUEsSUFBSztRQUFVO0tBQUE7Q0FDN0M7QUFhTSxRQUFJLGtFQUFpQixNQUFLLENBQVUiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXGljb25zXFx4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtcbiAgWydwYXRoJywgeyBkOiAnTTE4IDYgNiAxOCcsIGtleTogJzFibDVmOCcgfV0sXG4gIFsncGF0aCcsIHsgZDogJ202IDYgMTIgMTInLCBrZXk6ICdkOGJrNnYnIH1dLFxuXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIFhcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1UZ2dOaUEySURFNElpQXZQZ29nSUR4d1lYUm9JR1E5SW0wMklEWWdNVElnTVRJaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL3hcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBYID0gY3JlYXRlTHVjaWRlSWNvbigneCcsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBYO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/handle-element.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/resize/handle-element.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resizeElement: () => (/* binding */ resizeElement)\n/* harmony export */ });\n/* harmony import */ var _utils_is_svg_element_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/is-svg-element.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-svg-element.mjs\");\n/* harmony import */ var _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/resolve-elements.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n\n\n\nconst resizeHandlers = new WeakMap();\nlet observer;\nconst getSize = (borderBoxAxis, svgAxis, htmlAxis) => (target, borderBoxSize) => {\n    if (borderBoxSize && borderBoxSize[0]) {\n        return borderBoxSize[0][(borderBoxAxis + \"Size\")];\n    }\n    else if ((0,_utils_is_svg_element_mjs__WEBPACK_IMPORTED_MODULE_0__.isSVGElement)(target) && \"getBBox\" in target) {\n        return target.getBBox()[svgAxis];\n    }\n    else {\n        return target[htmlAxis];\n    }\n};\nconst getWidth = /*@__PURE__*/ getSize(\"inline\", \"width\", \"offsetWidth\");\nconst getHeight = /*@__PURE__*/ getSize(\"block\", \"height\", \"offsetHeight\");\nfunction notifyTarget({ target, borderBoxSize }) {\n    resizeHandlers.get(target)?.forEach((handler) => {\n        handler(target, {\n            get width() {\n                return getWidth(target, borderBoxSize);\n            },\n            get height() {\n                return getHeight(target, borderBoxSize);\n            },\n        });\n    });\n}\nfunction notifyAll(entries) {\n    entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n    if (typeof ResizeObserver === \"undefined\")\n        return;\n    observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n    if (!observer)\n        createResizeObserver();\n    const elements = (0,_utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_1__.resolveElements)(target);\n    elements.forEach((element) => {\n        let elementHandlers = resizeHandlers.get(element);\n        if (!elementHandlers) {\n            elementHandlers = new Set();\n            resizeHandlers.set(element, elementHandlers);\n        }\n        elementHandlers.add(handler);\n        observer?.observe(element);\n    });\n    return () => {\n        elements.forEach((element) => {\n            const elementHandlers = resizeHandlers.get(element);\n            elementHandlers?.delete(handler);\n            if (!elementHandlers?.size) {\n                observer?.unobserve(element);\n            }\n        });\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/handle-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/handle-window.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/resize/handle-window.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resizeWindow: () => (/* binding */ resizeWindow)\n/* harmony export */ });\nconst windowCallbacks = new Set();\nlet windowResizeHandler;\nfunction createWindowResizeHandler() {\n    windowResizeHandler = () => {\n        const info = {\n            get width() {\n                return window.innerWidth;\n            },\n            get height() {\n                return window.innerHeight;\n            },\n        };\n        windowCallbacks.forEach((callback) => callback(info));\n    };\n    window.addEventListener(\"resize\", windowResizeHandler);\n}\nfunction resizeWindow(callback) {\n    windowCallbacks.add(callback);\n    if (!windowResizeHandler)\n        createWindowResizeHandler();\n    return () => {\n        windowCallbacks.delete(callback);\n        if (!windowCallbacks.size &&\n            typeof windowResizeHandler === \"function\") {\n            window.removeEventListener(\"resize\", windowResizeHandler);\n            windowResizeHandler = undefined;\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvcmVzaXplL2hhbmRsZS13aW5kb3cubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFd0IiLCJzb3VyY2VzIjpbIkQ6XFxST1NITklcXGZvclJvc2huaVxcZm9yLXJvc2huaVxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xccmVzaXplXFxoYW5kbGUtd2luZG93Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB3aW5kb3dDYWxsYmFja3MgPSBuZXcgU2V0KCk7XG5sZXQgd2luZG93UmVzaXplSGFuZGxlcjtcbmZ1bmN0aW9uIGNyZWF0ZVdpbmRvd1Jlc2l6ZUhhbmRsZXIoKSB7XG4gICAgd2luZG93UmVzaXplSGFuZGxlciA9ICgpID0+IHtcbiAgICAgICAgY29uc3QgaW5mbyA9IHtcbiAgICAgICAgICAgIGdldCB3aWR0aCgpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gd2luZG93LmlubmVyV2lkdGg7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZ2V0IGhlaWdodCgpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gd2luZG93LmlubmVySGVpZ2h0O1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfTtcbiAgICAgICAgd2luZG93Q2FsbGJhY2tzLmZvckVhY2goKGNhbGxiYWNrKSA9PiBjYWxsYmFjayhpbmZvKSk7XG4gICAgfTtcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCB3aW5kb3dSZXNpemVIYW5kbGVyKTtcbn1cbmZ1bmN0aW9uIHJlc2l6ZVdpbmRvdyhjYWxsYmFjaykge1xuICAgIHdpbmRvd0NhbGxiYWNrcy5hZGQoY2FsbGJhY2spO1xuICAgIGlmICghd2luZG93UmVzaXplSGFuZGxlcilcbiAgICAgICAgY3JlYXRlV2luZG93UmVzaXplSGFuZGxlcigpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIHdpbmRvd0NhbGxiYWNrcy5kZWxldGUoY2FsbGJhY2spO1xuICAgICAgICBpZiAoIXdpbmRvd0NhbGxiYWNrcy5zaXplICYmXG4gICAgICAgICAgICB0eXBlb2Ygd2luZG93UmVzaXplSGFuZGxlciA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCB3aW5kb3dSZXNpemVIYW5kbGVyKTtcbiAgICAgICAgICAgIHdpbmRvd1Jlc2l6ZUhhbmRsZXIgPSB1bmRlZmluZWQ7XG4gICAgICAgIH1cbiAgICB9O1xufVxuXG5leHBvcnQgeyByZXNpemVXaW5kb3cgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/handle-window.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/resize/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resize: () => (/* binding */ resize)\n/* harmony export */ });\n/* harmony import */ var _handle_element_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./handle-element.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/handle-element.mjs\");\n/* harmony import */ var _handle_window_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handle-window.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/handle-window.mjs\");\n\n\n\nfunction resize(a, b) {\n    return typeof a === \"function\" ? (0,_handle_window_mjs__WEBPACK_IMPORTED_MODULE_0__.resizeWindow)(a) : (0,_handle_element_mjs__WEBPACK_IMPORTED_MODULE_1__.resizeElement)(a, b);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvcmVzaXplL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBcUQ7QUFDRjs7QUFFbkQ7QUFDQSxxQ0FBcUMsZ0VBQVksTUFBTSxrRUFBYTtBQUNwRTs7QUFFa0IiLCJzb3VyY2VzIjpbIkQ6XFxST1NITklcXGZvclJvc2huaVxcZm9yLXJvc2huaVxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xccmVzaXplXFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmVzaXplRWxlbWVudCB9IGZyb20gJy4vaGFuZGxlLWVsZW1lbnQubWpzJztcbmltcG9ydCB7IHJlc2l6ZVdpbmRvdyB9IGZyb20gJy4vaGFuZGxlLXdpbmRvdy5tanMnO1xuXG5mdW5jdGlvbiByZXNpemUoYSwgYikge1xuICAgIHJldHVybiB0eXBlb2YgYSA9PT0gXCJmdW5jdGlvblwiID8gcmVzaXplV2luZG93KGEpIDogcmVzaXplRWxlbWVudChhLCBiKTtcbn1cblxuZXhwb3J0IHsgcmVzaXplIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/scroll/observe.mjs":
/*!************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/scroll/observe.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   observeTimeline: () => (/* binding */ observeTimeline)\n/* harmony export */ });\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n\n\nfunction observeTimeline(update, timeline) {\n    let prevProgress;\n    const onFrame = () => {\n        const { currentTime } = timeline;\n        const percentage = currentTime === null ? 0 : currentTime.value;\n        const progress = percentage / 100;\n        if (prevProgress !== progress) {\n            update(progress);\n        }\n        prevProgress = progress;\n    };\n    _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame.preUpdate(onFrame, true);\n    return () => (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(onFrame);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvc2Nyb2xsL29ic2VydmUubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTREOztBQUU1RDtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsY0FBYztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksdURBQUs7QUFDVCxpQkFBaUIsaUVBQVc7QUFDNUI7O0FBRTJCIiwic291cmNlcyI6WyJEOlxcUk9TSE5JXFxmb3JSb3NobmlcXGZvci1yb3NobmlcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXHNjcm9sbFxcb2JzZXJ2ZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZnJhbWUsIGNhbmNlbEZyYW1lIH0gZnJvbSAnLi4vZnJhbWVsb29wL2ZyYW1lLm1qcyc7XG5cbmZ1bmN0aW9uIG9ic2VydmVUaW1lbGluZSh1cGRhdGUsIHRpbWVsaW5lKSB7XG4gICAgbGV0IHByZXZQcm9ncmVzcztcbiAgICBjb25zdCBvbkZyYW1lID0gKCkgPT4ge1xuICAgICAgICBjb25zdCB7IGN1cnJlbnRUaW1lIH0gPSB0aW1lbGluZTtcbiAgICAgICAgY29uc3QgcGVyY2VudGFnZSA9IGN1cnJlbnRUaW1lID09PSBudWxsID8gMCA6IGN1cnJlbnRUaW1lLnZhbHVlO1xuICAgICAgICBjb25zdCBwcm9ncmVzcyA9IHBlcmNlbnRhZ2UgLyAxMDA7XG4gICAgICAgIGlmIChwcmV2UHJvZ3Jlc3MgIT09IHByb2dyZXNzKSB7XG4gICAgICAgICAgICB1cGRhdGUocHJvZ3Jlc3MpO1xuICAgICAgICB9XG4gICAgICAgIHByZXZQcm9ncmVzcyA9IHByb2dyZXNzO1xuICAgIH07XG4gICAgZnJhbWUucHJlVXBkYXRlKG9uRnJhbWUsIHRydWUpO1xuICAgIHJldHVybiAoKSA9PiBjYW5jZWxGcmFtZShvbkZyYW1lKTtcbn1cblxuZXhwb3J0IHsgb2JzZXJ2ZVRpbWVsaW5lIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/scroll/observe.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/transform.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transform: () => (/* binding */ transform)\n/* harmony export */ });\n/* harmony import */ var _interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interpolate.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/interpolate.mjs\");\n\n\nfunction transform(...args) {\n    const useImmediate = !Array.isArray(args[0]);\n    const argOffset = useImmediate ? 0 : -1;\n    const inputValue = args[0 + argOffset];\n    const inputRange = args[1 + argOffset];\n    const outputRange = args[2 + argOffset];\n    const options = args[3 + argOffset];\n    const interpolator = (0,_interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__.interpolate)(inputRange, outputRange, options);\n    return useImmediate ? interpolator(inputValue) : interpolator;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvdHJhbnNmb3JtLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDs7QUFFaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsNkRBQVc7QUFDcEM7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkQ6XFxST1NITklcXGZvclJvc2huaVxcZm9yLXJvc2huaVxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcdXRpbHNcXHRyYW5zZm9ybS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW50ZXJwb2xhdGUgfSBmcm9tICcuL2ludGVycG9sYXRlLm1qcyc7XG5cbmZ1bmN0aW9uIHRyYW5zZm9ybSguLi5hcmdzKSB7XG4gICAgY29uc3QgdXNlSW1tZWRpYXRlID0gIUFycmF5LmlzQXJyYXkoYXJnc1swXSk7XG4gICAgY29uc3QgYXJnT2Zmc2V0ID0gdXNlSW1tZWRpYXRlID8gMCA6IC0xO1xuICAgIGNvbnN0IGlucHV0VmFsdWUgPSBhcmdzWzAgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IGlucHV0UmFuZ2UgPSBhcmdzWzEgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IG91dHB1dFJhbmdlID0gYXJnc1syICsgYXJnT2Zmc2V0XTtcbiAgICBjb25zdCBvcHRpb25zID0gYXJnc1szICsgYXJnT2Zmc2V0XTtcbiAgICBjb25zdCBpbnRlcnBvbGF0b3IgPSBpbnRlcnBvbGF0ZShpbnB1dFJhbmdlLCBvdXRwdXRSYW5nZSwgb3B0aW9ucyk7XG4gICAgcmV0dXJuIHVzZUltbWVkaWF0ZSA/IGludGVycG9sYXRvcihpbnB1dFZhbHVlKSA6IGludGVycG9sYXRvcjtcbn1cblxuZXhwb3J0IHsgdHJhbnNmb3JtIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CCelebrationRoom.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CFutureDreams.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CLandingPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CLoveLetter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CLoveStoryTimeline.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CPhotoGallery.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CSpinTheWheel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CVirtualHug.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CCelebrationRoom.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CFutureDreams.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CLandingPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CLoveLetter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CLoveStoryTimeline.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CPhotoGallery.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CSpinTheWheel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CVirtualHug.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CelebrationRoom.tsx */ \"(app-pages-browser)/./src/components/CelebrationRoom.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/FutureDreams.tsx */ \"(app-pages-browser)/./src/components/FutureDreams.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/LandingPage.tsx */ \"(app-pages-browser)/./src/components/LandingPage.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/LoveLetter.tsx */ \"(app-pages-browser)/./src/components/LoveLetter.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/LoveStoryTimeline.tsx */ \"(app-pages-browser)/./src/components/LoveStoryTimeline.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PhotoGallery.tsx */ \"(app-pages-browser)/./src/components/PhotoGallery.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SpinTheWheel.tsx */ \"(app-pages-browser)/./src/components/SpinTheWheel.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/VirtualHug.tsx */ \"(app-pages-browser)/./src/components/VirtualHug.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CCelebrationRoom.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CFutureDreams.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CLandingPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CLoveLetter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CLoveStoryTimeline.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CPhotoGallery.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CSpinTheWheel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CROSHNI%5C%5CforRoshni%5C%5Cfor-roshni%5C%5Csrc%5C%5Ccomponents%5C%5CVirtualHug.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/FutureDreams.tsx":
/*!*****************************************!*\
  !*** ./src/components/FutureDreams.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Ring,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plane.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Ring,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Ring,Star!=!lucide-react */ \"(app-pages-browser)/__barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Ring,Star!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Ring,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/baby.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Ring,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Ring,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Ring,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst FutureDreams = ()=>{\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        -100\n    ]);\n    const opacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        0.2,\n        0.8,\n        1\n    ], [\n        0,\n        1,\n        1,\n        0\n    ]);\n    const dreams = [\n        {\n            id: 1,\n            title: \"Travel the World Together\",\n            description: \"Exploring beautiful destinations, creating memories in every corner of the world\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 31,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-blue-400 to-cyan-500\",\n            details: \"Paris for romance, Tokyo for adventure, Maldives for relaxation, and so many more places to discover together, hand in hand.\"\n        },\n        {\n            id: 2,\n            title: \"Our Dream Home\",\n            description: \"A cozy place where we can build our life together, filled with love and laughter\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 39,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-green-400 to-emerald-500\",\n            details: \"A beautiful home with a garden where we can have morning coffee together, a cozy living room for movie nights, and a kitchen where we cook together.\"\n        },\n        {\n            id: 3,\n            title: \"Getting Married\",\n            description: \"The most beautiful day when we promise to love each other forever\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Ring, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 47,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-pink-400 to-rose-500\",\n            details: \"Our perfect wedding day, surrounded by family and friends, celebrating our eternal love. You in a beautiful white dress, me in a suit, both of us crying happy tears.\"\n        },\n        {\n            id: 4,\n            title: \"Our Little Family\",\n            description: \"Raising beautiful children who have your eyes and my sense of humor\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 55,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-yellow-400 to-orange-500\",\n            details: \"Little ones running around our home, teaching them to be kind and loving like their mama, watching them grow up in a house full of love.\"\n        },\n        {\n            id: 5,\n            title: \"Adventures Together\",\n            description: \"Hiking mountains, beach walks, road trips, and spontaneous adventures\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 63,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-purple-400 to-indigo-500\",\n            details: \"Weekend getaways, hiking trails with breathtaking views, beach sunsets, road trips with our favorite music, and capturing every beautiful moment.\"\n        },\n        {\n            id: 6,\n            title: \"Growing Old Together\",\n            description: \"Still being silly, still in love, with gray hair and wrinkled hands intertwined\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 71,\n                columnNumber: 13\n            }, undefined),\n            color: \"from-red-400 to-pink-500\",\n            details: \"Sitting on our porch at 80, still holding hands, still making each other laugh, still as in love as we are today. Our love story spanning decades.\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"min-h-screen bg-gradient-to-br from-indigo-100 via-purple-100 to-pink-100 py-16 px-4 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                style: {\n                    y,\n                    opacity\n                },\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    ...Array(15)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"absolute text-purple-200 opacity-30\",\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\")\n                        },\n                        animate: {\n                            y: [\n                                0,\n                                -30,\n                                0\n                            ],\n                            rotate: [\n                                0,\n                                360\n                            ],\n                            scale: [\n                                0.8,\n                                1.2,\n                                0.8\n                            ]\n                        },\n                        transition: {\n                            duration: 4 + Math.random() * 3,\n                            repeat: Infinity,\n                            delay: Math.random() * 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            size: 20 + Math.random() * 15,\n                            fill: \"currentColor\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, undefined)\n                    }, i, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600 mb-4\",\n                                children: \"Our Dreams Together\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: \"Every dream I have includes you, Roshni. Here's to our beautiful future together ✨\\uD83D\\uDC95\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-24\",\n                        children: dreams.map((dream, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: index % 2 === 0 ? -100 : 100\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: index * 0.1\n                                },\n                                className: \"flex items-center \".concat(index % 2 === 0 ? 'flex-row' : 'flex-row-reverse', \" gap-8\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 \".concat(index % 2 === 0 ? 'text-right' : 'text-left'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-3xl font-dancing-script text-gray-800 mb-4\",\n                                                    children: dream.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-lg leading-relaxed mb-4\",\n                                                    children: dream.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 leading-relaxed\",\n                                                    children: dream.details\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        whileHover: {\n                                            scale: 1.1,\n                                            rotate: 5\n                                        },\n                                        className: \"w-24 h-24 rounded-full bg-gradient-to-r \".concat(dream.color, \" flex items-center justify-center text-white shadow-xl flex-shrink-0\"),\n                                        children: dream.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, dream.id, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.5\n                        },\n                        className: \"text-center mt-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    animate: {\n                                        scale: [\n                                            1,\n                                            1.05,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    },\n                                    className: \"mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-16 h-16 text-pink-500 mx-auto\",\n                                        fill: \"currentColor\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-4xl font-dancing-script text-gray-800 mb-6\",\n                                    children: \"Forever and Always\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 leading-relaxed mb-8 max-w-3xl mx-auto\",\n                                    children: \"Every single one of these dreams feels possible because I have you by my side, Roshni. You make me believe in forever, in happily ever after, in love that lasts a lifetime. I can't wait to turn every one of these dreams into our beautiful reality.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center space-x-2 mb-6\",\n                                    children: [\n                                        ...Array(9)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4 text-pink-400 animate-pulse\",\n                                            fill: \"currentColor\",\n                                            style: {\n                                                animationDelay: \"\".concat(i * 0.2, \"s\")\n                                            }\n                                        }, i, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-dancing-script text-pink-600\",\n                                    children: \"With all my love, today and always ❤️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-500 mt-2\",\n                                    children: \"- Your Saurabh\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none overflow-hidden\",\n                        children: [\n                            ...Array(10)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                className: \"absolute\",\n                                style: {\n                                    left: \"\".concat(Math.random() * 100, \"%\"),\n                                    top: \"\".concat(Math.random() * 100, \"%\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -100,\n                                        0\n                                    ],\n                                    x: [\n                                        0,\n                                        Math.random() * 50 - 25,\n                                        0\n                                    ],\n                                    rotate: [\n                                        0,\n                                        360\n                                    ],\n                                    scale: [\n                                        0.5,\n                                        1,\n                                        0.5\n                                    ]\n                                },\n                                transition: {\n                                    duration: 6 + Math.random() * 4,\n                                    repeat: Infinity,\n                                    delay: Math.random() * 4\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-pink-300 opacity-40\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 30\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Camera_Heart_Home_Plane_Ring_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Ring, {}, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 40\n                                        }, undefined)\n                                    ][Math.floor(Math.random() * 3)]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, i, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\FutureDreams.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FutureDreams, \"zrUicr8NahEKota2lIHMY3jqlX0=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform\n    ];\n});\n_c = FutureDreams;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FutureDreams);\nvar _c;\n$RefreshReg$(_c, \"FutureDreams\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FutureDreams.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/PhotoGallery.tsx":
/*!*****************************************!*\
  !*** ./src/components/PhotoGallery.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Heart,Pause,Play,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Heart,Pause,Play,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Heart,Pause,Play,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Heart,Pause,Play,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Heart,Pause,Play,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst PhotoGallery = ()=>{\n    _s();\n    const [selectedPhoto, setSelectedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSlideshow, setIsSlideshow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSlideIndex, setCurrentSlideIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Placeholder photos - in a real implementation, these would be actual photos\n    const photos = [\n        {\n            id: 1,\n            src: \"/api/placeholder/300/400\",\n            caption: \"Cutest Smile\",\n            description: \"This is my favorite photo of you, Roshni. Your smile here just melts my heart every single time I look at it. ❤️\",\n            date: \"A beautiful day\"\n        },\n        {\n            id: 2,\n            src: \"/api/placeholder/300/400\",\n            caption: \"Best Hug\",\n            description: \"The warmest, most comforting hug ever. I felt so safe and loved in this moment with you, my beautiful babu. 🤗\",\n            date: \"Perfect moment\"\n        },\n        {\n            id: 3,\n            src: \"/api/placeholder/300/400\",\n            caption: \"My Princess\",\n            description: \"You look absolutely stunning here! Like a real princess, which you are to me every single day. 👑✨\",\n            date: \"Magical evening\"\n        },\n        {\n            id: 4,\n            src: \"/api/placeholder/300/400\",\n            caption: \"Silly Us\",\n            description: \"I love how goofy we can be together. These silly moments are some of my most treasured memories with you! 😄\",\n            date: \"Fun times\"\n        },\n        {\n            id: 5,\n            src: \"/api/placeholder/300/400\",\n            caption: \"Beautiful Eyes\",\n            description: \"Your eyes in this photo... I could get lost in them forever. They hold so much love, warmth, and beauty. 👀💕\",\n            date: \"Dreamy afternoon\"\n        },\n        {\n            id: 6,\n            src: \"/api/placeholder/300/400\",\n            caption: \"Together Forever\",\n            description: \"This photo represents everything I want - you and me, together, happy, and in love. Forever and always. 💑\",\n            date: \"Our future\"\n        }\n    ];\n    const startSlideshow = ()=>{\n        setIsSlideshow(true);\n        setCurrentSlideIndex(0);\n    };\n    const stopSlideshow = ()=>{\n        setIsSlideshow(false);\n    };\n    const nextSlide = ()=>{\n        setCurrentSlideIndex((prev)=>(prev + 1) % photos.length);\n    };\n    const prevSlide = ()=>{\n        setCurrentSlideIndex((prev)=>(prev - 1 + photos.length) % photos.length);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 py-16 px-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-orange-600 to-red-600 mb-4\",\n                                children: \"Our Beautiful Memories\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-2xl mx-auto mb-8\",\n                                children: \"Every photo tells a story of our love, Roshni \\uD83D\\uDCF8\\uD83D\\uDC95\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                onClick: startSlideshow,\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-3 rounded-full hover:from-orange-600 hover:to-red-600 transition-all font-medium flex items-center space-x-2 mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Start Slideshow\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: photos.map((photo, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.1\n                                },\n                                whileHover: {\n                                    y: -10,\n                                    rotate: Math.random() * 6 - 3\n                                },\n                                className: \"cursor-pointer\",\n                                onClick: ()=>setSelectedPhoto(photo),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-4 pb-16 shadow-xl transform rotate-1 hover:rotate-0 transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-hidden bg-gray-200 aspect-[3/4] mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full h-full bg-gradient-to-br from-pink-200 to-purple-200 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-16 h-16 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black/0 hover:bg-black/20 transition-all duration-300 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-8 h-8 text-white opacity-0 hover:opacity-100 transition-opacity\",\n                                                        fill: \"currentColor\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-dancing-script text-xl text-gray-800 mb-1\",\n                                                    children: photo.caption\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: photo.date\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, photo.id, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: selectedPhoto && !isSlideshow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4\",\n                    onClick: ()=>setSelectedPhoto(null),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            scale: 0.5,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.5,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-2xl p-8 max-w-2xl mx-auto shadow-2xl\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-dancing-script text-gray-800\",\n                                        children: selectedPhoto.caption\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedPhoto(null),\n                                        className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative overflow-hidden bg-gray-200 aspect-[3/4] mb-6 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-full bg-gradient-to-br from-pink-200 to-purple-200 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-24 h-24 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 leading-relaxed mb-4\",\n                                children: selectedPhoto.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-orange-500 font-medium text-center\",\n                                children: selectedPhoto.date\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: isSlideshow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black flex items-center justify-center z-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-8 right-8 flex space-x-4 z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: stopSlideshow,\n                                    className: \"bg-white/20 backdrop-blur-sm rounded-full p-3 text-white hover:bg-white/30 transition-all\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: stopSlideshow,\n                                    className: \"bg-white/20 backdrop-blur-sm rounded-full p-3 text-white hover:bg-white/30 transition-all\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: prevSlide,\n                            className: \"absolute left-8 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm rounded-full p-4 text-white hover:bg-white/30 transition-all z-10\",\n                            children: \"←\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: nextSlide,\n                            className: \"absolute right-8 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm rounded-full p-4 text-white hover:bg-white/30 transition-all z-10\",\n                            children: \"→\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-white max-w-4xl mx-auto px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 100\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    x: -100\n                                },\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative overflow-hidden bg-gray-800 aspect-[3/4] max-h-96 mx-auto mb-6 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-br from-pink-300 to-purple-300 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Heart_Pause_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-24 h-24 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-4xl font-dancing-script mb-4\",\n                                        children: photos[currentSlideIndex].caption\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg leading-relaxed mb-4 max-w-2xl mx-auto\",\n                                        children: photos[currentSlideIndex].description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-orange-300 font-medium\",\n                                        children: photos[currentSlideIndex].date\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, currentSlideIndex, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2\",\n                            children: photos.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentSlideIndex(index),\n                                    className: \"w-3 h-3 rounded-full transition-all \".concat(index === currentSlideIndex ? 'bg-white' : 'bg-white/40')\n                                }, index, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\PhotoGallery.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PhotoGallery, \"/YS6bqu/OM1T3qPs/fdUpPZ1K3A=\");\n_c = PhotoGallery;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PhotoGallery);\nvar _c;\n$RefreshReg$(_c, \"PhotoGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PhotoGallery.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/VirtualHug.tsx":
/*!***************************************!*\
  !*** ./src/components/VirtualHug.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Sparkles,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Sparkles,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Sparkles,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Sparkles,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst VirtualHug = ()=>{\n    _s();\n    const [isHugging, setIsHugging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHeartbeat, setShowHeartbeat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [audioEnabled, setAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hugCount, setHugCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const startHug = ()=>{\n        setIsHugging(true);\n        setShowHeartbeat(true);\n        setHugCount((prev)=>prev + 1);\n        // Simulate heartbeat sound effect\n        if (audioEnabled) {\n            // In a real implementation, you would play actual audio here\n            console.log('Playing heartbeat sound...');\n        }\n        // Reset after 5 seconds\n        setTimeout(()=>{\n            setIsHugging(false);\n            setShowHeartbeat(false);\n        }, 5000);\n    };\n    const toggleAudio = ()=>{\n        setAudioEnabled(!audioEnabled);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-rose-100 via-pink-100 to-purple-100 flex items-center justify-center py-16 px-4 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    ...Array(20)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute text-pink-300 opacity-20\",\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\")\n                        },\n                        animate: {\n                            y: [\n                                0,\n                                -50,\n                                0\n                            ],\n                            rotate: [\n                                0,\n                                360\n                            ],\n                            scale: [\n                                0.5,\n                                1.5,\n                                0.5\n                            ]\n                        },\n                        transition: {\n                            duration: 4 + Math.random() * 3,\n                            repeat: Infinity,\n                            delay: Math.random() * 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            size: 15 + Math.random() * 25,\n                            fill: \"currentColor\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, undefined)\n                    }, i, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto text-center relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h2, {\n                        initial: {\n                            opacity: 0,\n                            y: -50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-rose-600 to-pink-600 mb-8\",\n                        children: \"Missing Me?\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                        initial: {\n                            opacity: 0\n                        },\n                        whileInView: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.3\n                        },\n                        className: \"text-xl text-gray-600 mb-12 max-w-2xl mx-auto\",\n                        children: \"Whenever you miss me, just click the button below for a warm virtual hug from your Saurabh \\uD83E\\uDD17\\uD83D\\uDC95\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                        onClick: toggleAudio,\n                        className: \"absolute top-0 right-0 bg-white/20 backdrop-blur-sm rounded-full p-3 text-pink-500 hover:bg-white/30 transition-all mb-8\",\n                        children: audioEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 27\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 61\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.8\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-lg\",\n                            children: [\n                                \"Virtual hugs given: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-pink-600 font-bold\",\n                                    children: hugCount\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 33\n                                }, undefined),\n                                \" \\uD83E\\uDD17\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"relative mb-12\",\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        whileTap: {\n                            scale: 0.95\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                            onClick: startHug,\n                            disabled: isHugging,\n                            className: \"\\n              relative w-64 h-64 rounded-full text-white font-bold text-xl shadow-2xl transition-all duration-500\\n              \".concat(isHugging ? 'bg-gradient-to-r from-red-400 to-pink-400 cursor-not-allowed' : 'bg-gradient-to-r from-rose-500 to-pink-500 hover:from-rose-600 hover:to-pink-600', \"\\n            \"),\n                            animate: isHugging ? {\n                                scale: [\n                                    1,\n                                    1.1,\n                                    1\n                                ],\n                                boxShadow: [\n                                    \"0 0 0 0 rgba(236, 72, 153, 0.7)\",\n                                    \"0 0 0 20px rgba(236, 72, 153, 0)\",\n                                    \"0 0 0 0 rgba(236, 72, 153, 0)\"\n                                ]\n                            } : {},\n                            transition: {\n                                duration: 1,\n                                repeat: isHugging ? Infinity : 0\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-16 h-16 mb-4 \".concat(isHugging ? 'animate-pulse' : ''),\n                                            fill: \"currentColor\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg\",\n                                            children: isHugging ? 'Hugging...' : 'Click if you miss me'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, undefined),\n                                isHugging && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"absolute inset-0 rounded-full border-4 border-white\",\n                                    animate: {\n                                        scale: [\n                                            1,\n                                            2,\n                                            3\n                                        ],\n                                        opacity: [\n                                            1,\n                                            0.5,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        children: isHugging && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -50\n                            },\n                            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl max-w-2xl mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                animate: {\n                                    scale: [\n                                        1,\n                                        1.05,\n                                        1\n                                    ]\n                                },\n                                transition: {\n                                    duration: 1,\n                                    repeat: Infinity\n                                },\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-12 h-12 text-pink-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-dancing-script text-gray-800 mb-4\",\n                                        children: \"Here's a tight virtual hug, baby... \\uD83E\\uDD17\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 leading-relaxed mb-4\",\n                                        children: \"I'm wrapping my arms around you right now, holding you close to my heart. Feel my love surrounding you, keeping you warm and safe. You're never alone, Roshni - I'm always here with you, loving you endlessly.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center space-x-2\",\n                                        children: [\n                                            ...Array(7)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4 text-pink-400 animate-pulse\",\n                                                fill: \"currentColor\",\n                                                style: {\n                                                    animationDelay: \"\".concat(i * 0.2, \"s\")\n                                                }\n                                            }, i, false, {\n                                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        children: showHeartbeat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            exit: {\n                                opacity: 0\n                            },\n                            className: \"fixed inset-0 pointer-events-none z-0\",\n                            children: [\n                                ...Array(30)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"absolute w-2 h-2 bg-pink-400 rounded-full\",\n                                    style: {\n                                        left: \"\".concat(Math.random() * 100, \"%\"),\n                                        top: \"\".concat(Math.random() * 100, \"%\")\n                                    },\n                                    animate: {\n                                        scale: [\n                                            0,\n                                            1,\n                                            0\n                                        ],\n                                        opacity: [\n                                            0,\n                                            1,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 1,\n                                        repeat: Infinity,\n                                        delay: Math.random() * 2\n                                    }\n                                }, i, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        whileInView: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.5\n                        },\n                        className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            {\n                                title: \"Always Here\",\n                                message: \"No matter how far apart we are, my love reaches you instantly ❤️\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-8 h-8\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 21\n                                }, undefined)\n                            },\n                            {\n                                title: \"Thinking of You\",\n                                message: \"Every moment of every day, you're in my thoughts and in my heart 💭\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-8 h-8\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 21\n                                }, undefined)\n                            },\n                            {\n                                title: \"Forever Yours\",\n                                message: \"Distance means nothing when someone means everything to you 💕\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-8 h-8\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 21\n                                }, undefined)\n                            }\n                        ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.7 + index * 0.2\n                                },\n                                className: \"bg-white/60 backdrop-blur-sm rounded-xl p-6 text-center hover:bg-white/80 transition-all\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-pink-500 mb-4 flex justify-center\",\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-dancing-script text-xl text-gray-800 mb-2\",\n                                        children: item.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm leading-relaxed\",\n                                        children: item.message\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ROSHNI\\\\forRoshni\\\\for-roshni\\\\src\\\\components\\\\VirtualHug.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VirtualHug, \"jqx2JVD62gID9DWojKVdQur969M=\");\n_c = VirtualHug;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VirtualHug);\nvar _c;\n$RefreshReg$(_c, \"VirtualHug\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VirtualHug.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Ring,Star!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*******************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Baby,Camera,Heart,Home,Plane,Ring,Star!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*******************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Baby: () => (/* reexport safe */ _icons_baby_js__WEBPACK_IMPORTED_MODULE_0__["default"]),
/* harmony export */   Camera: () => (/* reexport safe */ _icons_camera_js__WEBPACK_IMPORTED_MODULE_1__["default"]),
/* harmony export */   Heart: () => (/* reexport safe */ _icons_heart_js__WEBPACK_IMPORTED_MODULE_2__["default"]),
/* harmony export */   Home: () => (/* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_3__["default"]),
/* harmony export */   Plane: () => (/* reexport safe */ _icons_plane_js__WEBPACK_IMPORTED_MODULE_4__["default"]),
/* harmony export */   Star: () => (/* reexport safe */ _icons_star_js__WEBPACK_IMPORTED_MODULE_5__["default"])
/* harmony export */ });
/* harmony import */ var _icons_baby_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/baby.js */ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/baby.js");
/* harmony import */ var _icons_camera_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/camera.js */ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js");
/* harmony import */ var _icons_heart_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/heart.js */ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js");
/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/house.js */ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js");
/* harmony import */ var _icons_plane_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/plane.js */ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plane.js");
/* harmony import */ var _icons_star_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/star.js */ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js");








;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ })

});