# 🎂 Happy Birthday Roshni! - A Digital Love Letter

A beautiful, interactive birthday website created with love for <PERSON><PERSON><PERSON> by <PERSON><PERSON><PERSON><PERSON>.

## ✨ Features

### 🎨 **Cinematic Design**
- Professional, bespoke romantic design
- Smooth animations powered by Framer Motion and GSAP
- Beautiful particle effects and floating elements
- Custom cursor with romantic interactions
- Glassmorphism and modern UI elements

### 🎂 **Interactive Sections**
1. **Hero Landing** - Autotyping birthday message with bokeh heart particles
2. **3D Birthday Cake** - Interactive Three.js cake with blowable candles
3. **Love Story Timeline** - Beautiful timeline of relationship milestones
4. **Animated Love Letter** - Envelope opening animation with heartfelt message
5. **Spin the Wheel of Love** - Interactive wheel revealing reasons of love
6. **Photo Gallery** - Polaroid-style gallery with slideshow
7. **Virtual Hug** - Emotional interaction with heartbeat effects
8. **Future Dreams** - Parallax scroll showing shared dreams
9. **Final Message** - Starry night conclusion with personal message

### 🎵 **Audio Experience**
- Background music manager with multiple romantic tracks
- Volume controls and track selection
- Music visualizer effects
- Ambient sound effects for interactions

### 📱 **Technical Excellence**
- Fully responsive design (mobile-first)
- Smooth scroll navigation with floating menu
- Loading screen with progress animation
- Custom particle systems
- Professional typography (Dancing Script + Inter)
- Optimized performance with Next.js 15

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd for-roshni
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS with custom romantic themes
- **Animations**: Framer Motion + GSAP
- **3D Graphics**: Three.js with React Three Fiber
- **Particles**: TSParticles for romantic effects
- **Audio**: React Howler for background music
- **Icons**: Lucide React
- **Fonts**: Google Fonts (Dancing Script, Inter)

## 📁 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and animations
│   ├── layout.tsx           # Root layout with fonts
│   └── page.tsx             # Main page with all components
├── components/
│   ├── LandingPage.tsx      # Hero section with autotyping
│   ├── CelebrationRoom.tsx  # Birthday celebration with balloons
│   ├── ThreeDCake.tsx       # Interactive 3D cake
│   ├── LoveStoryTimeline.tsx # Relationship timeline
│   ├── LoveLetter.tsx       # Animated love letter
│   ├── SpinTheWheel.tsx     # Interactive love wheel
│   ├── PhotoGallery.tsx     # Polaroid photo gallery
│   ├── VirtualHug.tsx       # Emotional hug interaction
│   ├── FutureDreams.tsx     # Parallax dreams section
│   ├── FinalMessage.tsx     # Concluding message
│   ├── LoadingScreen.tsx    # Beautiful loading animation
│   ├── FloatingNavigation.tsx # Smooth scroll navigation
│   ├── AudioManager.tsx     # Background music system
│   ├── CustomCursor.tsx     # Romantic cursor effects
│   └── ParticleBackground.tsx # Particle system
```

## 🎨 Design Philosophy

This website was designed to feel like a **bespoke romantic digital experience**, not a generic template. Every element was carefully crafted with:

- **Emotional storytelling** through smooth animations
- **Cinematic quality** with depth and layered visuals
- **Personal touches** in every interaction
- **Professional polish** with attention to detail
- **Romantic aesthetics** using soft gradients and particle effects

## 🌟 Key Interactions

- **Candle Blowing**: Click on 3D cake candles to blow them out
- **Wheel Spinning**: Spin to reveal sweet love messages
- **Photo Viewing**: Hover and click polaroid photos
- **Virtual Hugging**: Emotional button with heartbeat effects
- **Timeline Exploration**: Click timeline events for detailed stories
- **Letter Reading**: Envelope opening animation
- **Music Control**: Floating audio controls with track selection

## 📱 Mobile Optimization

- Touch-friendly interactions
- Responsive typography scaling
- Optimized particle counts for performance
- Swipe gestures for photo gallery
- Mobile-first responsive design

## 🚀 Deployment

### Vercel (Recommended)
```bash
npm run build
# Deploy to Vercel
```

### Netlify
```bash
npm run build
npm run export
# Upload dist folder to Netlify
```

### Custom Domain Setup
For a custom domain like `roshnibirthday.com`:
1. Purchase domain from registrar
2. Configure DNS settings
3. Add domain to hosting platform
4. Enable HTTPS

## 💝 Personal Message

This website represents hours of love, creativity, and technical craftsmanship. Every animation, every color choice, every word was chosen to make Roshni feel special on her birthday.

**From Saurabh with all my love ❤️**

---

## 📄 License

This is a personal gift project. All rights reserved.

**Made with ❤️ for Roshni's Birthday 2024**
