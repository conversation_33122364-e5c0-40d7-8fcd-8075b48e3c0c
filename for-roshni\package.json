{"name": "for-roshni", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.4", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "@types/howler": "^2.2.12", "@types/three": "^0.177.0", "framer-motion": "^12.20.1", "gsap": "^3.13.0", "lucide-react": "^0.525.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-howler": "^5.2.0", "react-particles": "^2.12.2", "three": "^0.178.0", "tsparticles": "^3.8.1", "tsparticles-engine": "^2.12.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}