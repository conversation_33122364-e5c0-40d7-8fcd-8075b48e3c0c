@import "tailwindcss";

:root {
  --font-inter: var(--font-inter);
  --font-dancing-script: var(--font-dancing-script);
}

@theme inline {
  --font-sans: var(--font-inter);
  --font-serif: var(--font-dancing-script);
}

/* Custom romantic animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes sparkle {
  0%, 100% { opacity: 0; transform: scale(0); }
  50% { opacity: 1; transform: scale(1); }
}

@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink {
  0%, 50% { border-color: transparent; }
  51%, 100% { border-color: #ec4899; }
}

/* Utility classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-heartbeat {
  animation: heartbeat 2s ease-in-out infinite;
}

.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-sparkle {
  animation: sparkle 1.5s ease-in-out infinite;
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid #ec4899;
  white-space: nowrap;
  animation: typewriter 3s steps(40, end), blink 0.75s step-end infinite;
}

/* Romantic gradient backgrounds */
.bg-romantic-gradient {
  background: linear-gradient(135deg, #fce7f3 0%, #f3e8ff 25%, #fdf2f8 50%, #fef7cd 75%, #fce7f3 100%);
}

.bg-love-gradient {
  background: linear-gradient(45deg, #ff6b9d, #c44569, #f8b500, #ff6b9d);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
}

.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #fdf2f8;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #ec4899, #be185d);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #be185d, #9d174d);
}
