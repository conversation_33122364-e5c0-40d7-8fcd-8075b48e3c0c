'use client';

import { useState } from 'react';
import LandingPage from '@/components/LandingPage';
import CelebrationRoom from '@/components/CelebrationRoom';
import LoveStoryTimeline from '@/components/LoveStoryTimeline';
import LoveLetter from '@/components/LoveLetter';
import SpinTheWheel from '@/components/SpinTheWheel';
import PhotoGallery from '@/components/PhotoGallery';
import VirtualHug from '@/components/VirtualHug';
import FutureDreams from '@/components/FutureDreams';
import FloatingNavigation from '@/components/FloatingNavigation';
import LoadingScreen from '@/components/LoadingScreen';
import AudioManager from '@/components/AudioManager';
import CustomCursor from '@/components/CustomCursor';
import FinalMessage from '@/components/FinalMessage';

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);

  const handleLoadingComplete = () => {
    setIsLoading(false);
  };

  if (isLoading) {
    return <LoadingScreen onLoadingComplete={handleLoadingComplete} />;
  }

  return (
    <main className="overflow-x-hidden cursor-none">
      <LandingPage />
      <CelebrationRoom />
      <LoveStoryTimeline />
      <LoveLetter />
      <SpinTheWheel />
      <PhotoGallery />
      <VirtualHug />
      <FutureDreams />
      <FinalMessage />
      <FloatingNavigation />
      <AudioManager />
      <CustomCursor />
    </main>
  );
}
