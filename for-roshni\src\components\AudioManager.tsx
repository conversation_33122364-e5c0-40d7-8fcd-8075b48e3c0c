'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Volume2, VolumeX, Music, Pause, Play } from 'lucide-react';

interface AudioTrack {
  id: string;
  name: string;
  src: string;
  section: string;
}

const AudioManager = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState(0.3);
  const [currentTrack, setCurrentTrack] = useState<AudioTrack | null>(null);
  const [showControls, setShowControls] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Audio tracks for different sections
  const tracks: AudioTrack[] = [
    {
      id: 'romantic',
      name: 'Romantic Ambience',
      src: '/audio/romantic-ambient.mp3', // You would add actual audio files
      section: 'landing'
    },
    {
      id: 'celebration',
      name: 'Birthday Celebration',
      src: '/audio/birthday-celebration.mp3',
      section: 'celebration'
    },
    {
      id: 'love-story',
      name: 'Our Love Story',
      src: '/audio/love-story.mp3',
      section: 'timeline'
    },
    {
      id: 'tender',
      name: 'Tender Moments',
      src: '/audio/tender-moments.mp3',
      section: 'letter'
    }
  ];

  useEffect(() => {
    // Auto-play romantic music on load (with user interaction)
    const handleFirstInteraction = () => {
      if (!currentTrack) {
        setCurrentTrack(tracks[0]);
        setIsPlaying(true);
      }
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('keydown', handleFirstInteraction);
    };

    document.addEventListener('click', handleFirstInteraction);
    document.addEventListener('keydown', handleFirstInteraction);

    return () => {
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('keydown', handleFirstInteraction);
    };
  }, [currentTrack]);

  useEffect(() => {
    if (audioRef.current && currentTrack) {
      audioRef.current.src = currentTrack.src;
      audioRef.current.volume = isMuted ? 0 : volume;
      audioRef.current.loop = true;
      
      if (isPlaying) {
        audioRef.current.play().catch(console.error);
      } else {
        audioRef.current.pause();
      }
    }
  }, [currentTrack, isPlaying, volume, isMuted]);

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const changeTrack = (track: AudioTrack) => {
    setCurrentTrack(track);
    setIsPlaying(true);
  };

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : newVolume;
    }
  };

  return (
    <>
      {/* Hidden Audio Element */}
      <audio ref={audioRef} preload="metadata" />

      {/* Floating Audio Controls */}
      <motion.div
        className="fixed top-8 left-8 z-50"
        onMouseEnter={() => setShowControls(true)}
        onMouseLeave={() => setShowControls(false)}
      >
        {/* Main Control Button */}
        <motion.button
          onClick={togglePlay}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="w-14 h-14 bg-white/20 backdrop-blur-sm rounded-full shadow-lg flex items-center justify-center text-pink-500 hover:bg-white/30 transition-all"
        >
          {isPlaying ? (
            <Pause className="w-6 h-6" />
          ) : (
            <Play className="w-6 h-6" />
          )}
        </motion.button>

        {/* Extended Controls */}
        <AnimatePresence>
          {showControls && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="absolute top-16 left-0 bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-xl min-w-64"
            >
              {/* Current Track Info */}
              {currentTrack && (
                <div className="mb-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Music className="w-4 h-4 text-pink-500" />
                    <span className="text-sm font-medium text-gray-700">
                      {currentTrack.name}
                    </span>
                  </div>
                </div>
              )}

              {/* Volume Control */}
              <div className="mb-4">
                <div className="flex items-center space-x-2 mb-2">
                  <button
                    onClick={toggleMute}
                    className="text-pink-500 hover:text-pink-600 transition-colors"
                  >
                    {isMuted ? (
                      <VolumeX className="w-4 h-4" />
                    ) : (
                      <Volume2 className="w-4 h-4" />
                    )}
                  </button>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={isMuted ? 0 : volume}
                    onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                    className="flex-1 h-2 bg-pink-200 rounded-lg appearance-none cursor-pointer"
                    style={{
                      background: `linear-gradient(to right, #ec4899 0%, #ec4899 ${(isMuted ? 0 : volume) * 100}%, #fce7f3 ${(isMuted ? 0 : volume) * 100}%, #fce7f3 100%)`
                    }}
                  />
                </div>
              </div>

              {/* Track Selection */}
              <div>
                <p className="text-xs text-gray-500 mb-2">Choose Music:</p>
                <div className="space-y-1">
                  {tracks.map((track) => (
                    <button
                      key={track.id}
                      onClick={() => changeTrack(track)}
                      className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-all ${
                        currentTrack?.id === track.id
                          ? 'bg-pink-100 text-pink-700 font-medium'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      {track.name}
                    </button>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Music Visualizer (Optional) */}
      {isPlaying && !isMuted && (
        <motion.div
          className="fixed bottom-8 left-8 z-40"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
        >
          <div className="flex items-end space-x-1">
            {[...Array(5)].map((_, i) => (
              <motion.div
                key={i}
                className="w-1 bg-gradient-to-t from-pink-400 to-purple-400 rounded-full"
                animate={{
                  height: [8, 20, 8],
                }}
                transition={{
                  duration: 0.5 + Math.random() * 0.5,
                  repeat: Infinity,
                  delay: i * 0.1,
                }}
              />
            ))}
          </div>
        </motion.div>
      )}
    </>
  );
};

export default AudioManager;
