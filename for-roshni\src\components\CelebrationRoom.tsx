'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Sparkles, Gift } from 'lucide-react';

const CelebrationRoom = () => {
  const [candlesBlown, setCandlesBlown] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [showWishCard, setShowWishCard] = useState(false);
  const [balloons, setBalloons] = useState<Array<{ id: number; text: string; color: string }>>([]);

  const totalCandles = 5;
  const balloonMessages = [
    "I Love You ❤️",
    "My Queen 👑",
    "<PERSON><PERSON><PERSON> 💘",
    "Forever Yours 💕",
    "Happy Birthday 🎂"
  ];

  const balloonColors = [
    "from-pink-400 to-pink-600",
    "from-purple-400 to-purple-600",
    "from-rose-400 to-rose-600",
    "from-red-400 to-red-600",
    "from-yellow-400 to-yellow-600"
  ];

  useEffect(() => {
    // Generate floating balloons
    const newBalloons = balloonMessages.map((text, index) => ({
      id: index,
      text,
      color: balloonColors[index % balloonColors.length]
    }));
    setBalloons(newBalloons);
  }, []);

  const blowCandle = (candleIndex: number) => {
    if (candlesBlown < totalCandles) {
      setCandlesBlown(prev => prev + 1);
      
      if (candlesBlown + 1 === totalCandles) {
        setShowConfetti(true);
        setTimeout(() => setShowWishCard(true), 1000);
      }
    }
  };

  const Confetti = () => (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {[...Array(50)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-2 h-2 bg-gradient-to-r from-yellow-400 to-pink-500 rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: '-10px',
          }}
          animate={{
            y: window.innerHeight + 100,
            rotate: 360,
            x: [0, Math.random() * 200 - 100],
          }}
          transition={{
            duration: 3 + Math.random() * 2,
            ease: "easeOut",
            delay: Math.random() * 2,
          }}
        />
      ))}
    </div>
  );

  return (
    <div id="celebration" className="min-h-screen bg-gradient-to-br from-purple-100 via-pink-100 to-rose-100 relative overflow-hidden">
      {/* Floating Balloons */}
      <div className="absolute inset-0 pointer-events-none">
        {balloons.map((balloon, index) => (
          <motion.div
            key={balloon.id}
            className="absolute"
            style={{
              left: `${10 + index * 18}%`,
              top: '10%',
            }}
            animate={{
              y: [0, -20, 0],
              rotate: [-5, 5, -5],
            }}
            transition={{
              duration: 3 + Math.random(),
              repeat: Infinity,
              delay: index * 0.5,
            }}
          >
            {/* Balloon String */}
            <div className="w-0.5 h-32 bg-gray-400 mx-auto mb-2"></div>
            
            {/* Balloon */}
            <div className={`w-16 h-20 rounded-full bg-gradient-to-br ${balloon.color} shadow-lg relative`}>
              <div className="absolute inset-0 rounded-full bg-white/20"></div>
              <div className="absolute top-2 left-2 w-4 h-4 rounded-full bg-white/40"></div>
              
              {/* Balloon Text */}
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs font-medium text-gray-700 whitespace-nowrap">
                {balloon.text}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Main Content */}
      <div className="flex flex-col items-center justify-center min-h-screen px-4 relative z-10">
        <motion.h2
          initial={{ opacity: 0, y: -50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-purple-600 mb-8 text-center"
        >
          Happy Birthday Roshni! 🎂
        </motion.h2>

        {/* Birthday Cake */}
        <motion.div
          initial={{ scale: 0 }}
          whileInView={{ scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative mb-8"
        >
          {/* Cake Base */}
          <div className="w-64 h-32 bg-gradient-to-t from-yellow-200 to-yellow-100 rounded-lg shadow-xl relative">
            {/* Cake Layers */}
            <div className="absolute top-0 w-full h-8 bg-gradient-to-t from-pink-300 to-pink-200 rounded-t-lg"></div>
            <div className="absolute top-8 w-full h-8 bg-gradient-to-t from-purple-300 to-purple-200"></div>
            
            {/* Candles */}
            <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 flex space-x-4">
              {[...Array(totalCandles)].map((_, index) => (
                <motion.div
                  key={index}
                  className="relative cursor-pointer"
                  onClick={() => blowCandle(index)}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  {/* Candle */}
                  <div className="w-2 h-8 bg-gradient-to-t from-red-400 to-red-300 rounded-sm"></div>
                  
                  {/* Flame */}
                  {candlesBlown <= index && (
                    <motion.div
                      className="absolute -top-3 left-1/2 transform -translate-x-1/2"
                      animate={{
                        scale: [1, 1.2, 1],
                        rotate: [-5, 5, -5],
                      }}
                      transition={{
                        duration: 0.5,
                        repeat: Infinity,
                      }}
                    >
                      <div className="w-3 h-4 bg-gradient-to-t from-orange-400 to-yellow-300 rounded-full"></div>
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Instructions */}
        {candlesBlown < totalCandles && (
          <motion.p
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 1 }}
            className="text-lg text-gray-600 text-center mb-4"
          >
            Click on the candles to blow them out! 🕯️
          </motion.p>
        )}

        {/* Progress */}
        <div className="text-center mb-8">
          <p className="text-gray-600">
            Candles blown: {candlesBlown}/{totalCandles}
          </p>
        </div>
      </div>

      {/* Confetti */}
      <AnimatePresence>
        {showConfetti && <Confetti />}
      </AnimatePresence>

      {/* Birthday Wish Card */}
      <AnimatePresence>
        {showWishCard && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.5 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ rotateY: -90 }}
              animate={{ rotateY: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-xl p-8 max-w-md mx-auto shadow-2xl"
            >
              <div className="text-center">
                <Gift className="w-16 h-16 text-pink-500 mx-auto mb-4" />
                <h3 className="text-2xl font-dancing-script text-gray-800 mb-4">
                  Birthday Wish from Saurabh 💕
                </h3>
                <p className="text-gray-600 leading-relaxed mb-6">
                  "Happy Birthday to the most beautiful, amazing, and wonderful girl in my life! 
                  You bring so much joy, love, and happiness into my world. 
                  I'm so grateful to have you as my girlfriend, my best friend, and my everything. 
                  Here's to many more birthdays together, my love! ❤️"
                </p>
                <button
                  onClick={() => setShowWishCard(false)}
                  className="bg-gradient-to-r from-pink-500 to-purple-500 text-white px-6 py-2 rounded-full hover:from-pink-600 hover:to-purple-600 transition-all"
                >
                  Thank you, Jaan! 💖
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CelebrationRoom;
