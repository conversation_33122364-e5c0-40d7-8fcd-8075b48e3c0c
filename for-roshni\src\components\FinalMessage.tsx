'use client';

import { motion } from 'framer-motion';
import { <PERSON>, <PERSON>rk<PERSON>, <PERSON>, Calendar } from 'lucide-react';

const FinalMessage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-900 via-pink-900 to-purple-900 flex items-center justify-center py-16 px-4 relative overflow-hidden">
      {/* Starry Background */}
      <div className="absolute inset-0">
        {[...Array(100)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.3, 1, 0.3],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 2 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          />
        ))}
      </div>

      <div className="max-w-4xl mx-auto text-center relative z-10">
        {/* Main Message */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
          className="mb-16"
        >
          <motion.div
            animate={{ 
              scale: [1, 1.1, 1],
              rotate: [0, 5, -5, 0]
            }}
            transition={{ 
              duration: 4, 
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="w-32 h-32 mx-auto mb-8 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full flex items-center justify-center shadow-2xl"
          >
            <Heart className="w-16 h-16 text-white" fill="currentColor" />
          </motion.div>

          <h1 className="text-5xl md:text-7xl font-dancing-script text-white mb-8 leading-tight">
            Happy Birthday, Roshni!
          </h1>
          
          <p className="text-xl md:text-2xl text-pink-200 mb-12 max-w-3xl mx-auto leading-relaxed">
            This website is my love letter to you, my gift to celebrate the amazing person you are. 
            Every section, every animation, every word was crafted with love, just for you.
          </p>
        </motion.div>

        {/* Stats/Highlights */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.8 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16"
        >
          {[
            {
              icon: <Heart className="w-8 h-8" />,
              title: "Infinite Love",
              description: "My love for you grows stronger every day"
            },
            {
              icon: <Sparkles className="w-8 h-8" />,
              title: "Magical Moments",
              description: "Every moment with you feels like magic"
            },
            {
              icon: <Gift className="w-8 h-8" />,
              title: "Special Gift",
              description: "This website is just the beginning of my gifts to you"
            }
          ].map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 + index * 0.2, duration: 0.6 }}
              className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20"
            >
              <div className="text-pink-300 mb-4 flex justify-center">
                {item.icon}
              </div>
              <h3 className="text-xl font-bold text-white mb-2">
                {item.title}
              </h3>
              <p className="text-pink-200 text-sm">
                {item.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        {/* Personal Message */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ delay: 1, duration: 0.8 }}
          className="bg-white/10 backdrop-blur-sm rounded-3xl p-12 border border-white/20 mb-16"
        >
          <h2 className="text-3xl font-dancing-script text-white mb-6">
            From Saurabh, with all my love ❤️
          </h2>
          <p className="text-pink-200 leading-relaxed text-lg mb-6">
            Roshni, you are the sunshine in my cloudy days, the calm in my storms, 
            and the joy in my heart. On your special day, I want you to know that 
            you are loved, cherished, and celebrated not just today, but every single day.
          </p>
          <p className="text-pink-200 leading-relaxed text-lg mb-8">
            Thank you for being you - beautiful, kind, funny, smart, and absolutely perfect. 
            Thank you for choosing to love me back. Here's to many more birthdays together, 
            my beautiful babu.
          </p>
          
          <div className="flex justify-center space-x-2 mb-6">
            {[...Array(11)].map((_, i) => (
              <Heart
                key={i}
                className="w-4 h-4 text-pink-300 animate-pulse"
                fill="currentColor"
                style={{ animationDelay: `${i * 0.2}s` }}
              />
            ))}
          </div>
          
          <p className="text-2xl font-dancing-script text-pink-300">
            Forever yours, Saurabh 💕
          </p>
        </motion.div>

        {/* Birthday Reminder */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.5, duration: 0.8 }}
          className="flex items-center justify-center space-x-4 text-pink-200"
        >
          <Calendar className="w-6 h-6" />
          <span className="text-lg">
            Made with ❤️ for your special day
          </span>
          <Calendar className="w-6 h-6" />
        </motion.div>

        {/* Floating Hearts */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(15)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute text-pink-300 opacity-30"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -100, 0],
                rotate: [0, 360],
                scale: [0.5, 1.5, 0.5],
              }}
              transition={{
                duration: 6 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 4,
              }}
            >
              <Heart size={20 + Math.random() * 20} fill="currentColor" />
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FinalMessage;
