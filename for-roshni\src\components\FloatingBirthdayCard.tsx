'use client';

import { useRef, Suspense } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Text, Float, Environment, Sparkles as ThreeSparkles } from '@react-three/drei';
import { motion } from 'framer-motion';
import * as THREE from 'three';

const FloatingCard = () => {
  const cardRef = useRef<THREE.Group>(null);
  const textRef = useRef<THREE.Group>(null);

  useFrame((state) => {
    if (cardRef.current) {
      cardRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
      cardRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.3) * 0.05;
    }
    if (textRef.current) {
      textRef.current.position.y = Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }
  });

  return (
    <Float speed={2} rotationIntensity={0.1} floatIntensity={0.2}>
      <group ref={cardRef}>
        {/* Card Background */}
        <mesh position={[0, 0, -0.01]}>
          <planeGeometry args={[4, 2.5]} />
          <meshStandardMaterial 
            color="#fdf2f8" 
            transparent 
            opacity={0.9}
            roughness={0.1}
            metalness={0.1}
          />
        </mesh>
        
        {/* Card Border */}
        <mesh position={[0, 0, 0]}>
          <planeGeometry args={[4.1, 2.6]} />
          <meshStandardMaterial 
            color="#ec4899" 
            transparent 
            opacity={0.3}
          />
        </mesh>
        
        {/* Text Group */}
        <group ref={textRef}>
          <Text
            position={[0, 0.5, 0.01]}
            fontSize={0.3}
            color="#be185d"
            anchorX="center"
            anchorY="middle"
            font="/fonts/dancing-script.woff"
          >
            Happy Birthday
          </Text>
          
          <Text
            position={[0, 0, 0.01]}
            fontSize={0.4}
            color="#ec4899"
            anchorX="center"
            anchorY="middle"
            font="/fonts/dancing-script.woff"
          >
            Roshni Jwala
          </Text>
          
          <Text
            position={[0, -0.5, 0.01]}
            fontSize={0.15}
            color="#be185d"
            anchorX="center"
            anchorY="middle"
          >
            From Saurabh with Love ❤️
          </Text>
        </group>
        
        {/* Sparkles around the card */}
        <ThreeSparkles
          count={20}
          scale={[6, 6, 6]}
          size={2}
          speed={0.3}
          color="#ec4899"
        />
      </group>
    </Float>
  );
};

const FloatingBirthdayCard = () => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.5 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 2, delay: 4 }}
      className="absolute top-1/4 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-40 pointer-events-none"
    >
      <Canvas camera={{ position: [0, 0, 5], fov: 50 }}>
        <Suspense fallback={null}>
          <ambientLight intensity={0.5} />
          <pointLight position={[10, 10, 10]} intensity={0.8} color="#ec4899" />
          <pointLight position={[-10, -10, 10]} intensity={0.5} color="#a855f7" />
          
          <Environment preset="sunset" />
          
          <FloatingCard />
        </Suspense>
      </Canvas>
    </motion.div>
  );
};

export default FloatingBirthdayCard;
