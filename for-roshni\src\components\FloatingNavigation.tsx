'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Heart, 
  Cake, 
  Clock, 
  Mail, 
  RotateCcw, 
  Camera, 
  Sparkles, 
  Star,
  Menu,
  X
} from 'lucide-react';

interface NavItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  color: string;
}

const FloatingNavigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('landing');

  const navItems: NavItem[] = [
    {
      id: 'landing',
      label: 'Home',
      icon: <Heart className="w-5 h-5" />,
      color: 'from-pink-500 to-rose-500'
    },
    {
      id: 'celebration',
      label: 'Birthday',
      icon: <Cake className="w-5 h-5" />,
      color: 'from-purple-500 to-pink-500'
    },
    {
      id: 'timeline',
      label: 'Our Story',
      icon: <Clock className="w-5 h-5" />,
      color: 'from-blue-500 to-purple-500'
    },
    {
      id: 'letter',
      label: 'Love Letter',
      icon: <Mail className="w-5 h-5" />,
      color: 'from-red-500 to-pink-500'
    },
    {
      id: 'wheel',
      label: 'Spin Wheel',
      icon: <RotateCcw className="w-5 h-5" />,
      color: 'from-purple-500 to-indigo-500'
    },
    {
      id: 'gallery',
      label: 'Photos',
      icon: <Camera className="w-5 h-5" />,
      color: 'from-orange-500 to-red-500'
    },
    {
      id: 'hug',
      label: 'Virtual Hug',
      icon: <Sparkles className="w-5 h-5" />,
      color: 'from-rose-500 to-pink-500'
    },
    {
      id: 'dreams',
      label: 'Our Future',
      icon: <Star className="w-5 h-5" />,
      color: 'from-indigo-500 to-purple-500'
    }
  ];

  useEffect(() => {
    const handleScroll = () => {
      const sections = navItems.map(item => item.id);
      const scrollPosition = window.scrollY + window.innerHeight / 2;

      for (const sectionId of sections) {
        const element = document.getElementById(sectionId);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(sectionId);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsOpen(false);
  };

  return (
    <div className="fixed bottom-8 right-8 z-50">
      {/* Main Menu Button */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        className="w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full shadow-2xl flex items-center justify-center text-white"
      >
        <AnimatePresence mode="wait">
          {isOpen ? (
            <motion.div
              key="close"
              initial={{ rotate: -90, opacity: 0 }}
              animate={{ rotate: 0, opacity: 1 }}
              exit={{ rotate: 90, opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <X className="w-6 h-6" />
            </motion.div>
          ) : (
            <motion.div
              key="menu"
              initial={{ rotate: 90, opacity: 0 }}
              animate={{ rotate: 0, opacity: 1 }}
              exit={{ rotate: -90, opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <Menu className="w-6 h-6" />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.button>

      {/* Navigation Items */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute bottom-20 right-0 space-y-3"
          >
            {navItems.map((item, index) => (
              <motion.button
                key={item.id}
                initial={{ x: 100, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: 100, opacity: 0 }}
                transition={{ delay: index * 0.05, duration: 0.3 }}
                onClick={() => scrollToSection(item.id)}
                whileHover={{ scale: 1.05, x: -5 }}
                whileTap={{ scale: 0.95 }}
                className={`
                  flex items-center space-x-3 px-4 py-3 rounded-full shadow-lg text-white font-medium
                  bg-gradient-to-r ${item.color} hover:shadow-xl transition-all duration-300
                  ${activeSection === item.id ? 'ring-2 ring-white ring-opacity-50' : ''}
                `}
              >
                {item.icon}
                <span className="text-sm whitespace-nowrap">{item.label}</span>
              </motion.button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Active Section Indicator */}
      <motion.div
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        className="absolute -top-2 -left-2 w-6 h-6 bg-white rounded-full shadow-lg flex items-center justify-center"
      >
        <div className="w-3 h-3 bg-gradient-to-r from-pink-400 to-purple-400 rounded-full animate-pulse"></div>
      </motion.div>
    </div>
  );
};

export default FloatingNavigation;
