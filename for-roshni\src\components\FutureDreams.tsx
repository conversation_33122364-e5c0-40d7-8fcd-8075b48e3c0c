'use client';

import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef } from 'react';
import { Plane, Home, Heart, Baby, MapPin, Camera, Crown, Star } from 'lucide-react';

interface Dream {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  details: string;
}

const FutureDreams = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [0, -100]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);

  const dreams: Dream[] = [
    {
      id: 1,
      title: "Travel the World Together",
      description: "Exploring beautiful destinations, creating memories in every corner of the world",
      icon: <Plane className="w-8 h-8" />,
      color: "from-blue-400 to-cyan-500",
      details: "Paris for romance, Tokyo for adventure, Maldives for relaxation, and so many more places to discover together, hand in hand."
    },
    {
      id: 2,
      title: "Our Dream Home",
      description: "A cozy place where we can build our life together, filled with love and laughter",
      icon: <Home className="w-8 h-8" />,
      color: "from-green-400 to-emerald-500",
      details: "A beautiful home with a garden where we can have morning coffee together, a cozy living room for movie nights, and a kitchen where we cook together."
    },
    {
      id: 3,
      title: "Getting Married",
      description: "The most beautiful day when we promise to love each other forever",
      icon: <Crown className="w-8 h-8" />,
      color: "from-pink-400 to-rose-500",
      details: "Our perfect wedding day, surrounded by family and friends, celebrating our eternal love. You in a beautiful white dress, me in a suit, both of us crying happy tears."
    },
    {
      id: 4,
      title: "Our Little Family",
      description: "Raising beautiful children who have your eyes and my sense of humor",
      icon: <Baby className="w-8 h-8" />,
      color: "from-yellow-400 to-orange-500",
      details: "Little ones running around our home, teaching them to be kind and loving like their mama, watching them grow up in a house full of love."
    },
    {
      id: 5,
      title: "Adventures Together",
      description: "Hiking mountains, beach walks, road trips, and spontaneous adventures",
      icon: <Camera className="w-8 h-8" />,
      color: "from-purple-400 to-indigo-500",
      details: "Weekend getaways, hiking trails with breathtaking views, beach sunsets, road trips with our favorite music, and capturing every beautiful moment."
    },
    {
      id: 6,
      title: "Growing Old Together",
      description: "Still being silly, still in love, with gray hair and wrinkled hands intertwined",
      icon: <Heart className="w-8 h-8" />,
      color: "from-red-400 to-pink-500",
      details: "Sitting on our porch at 80, still holding hands, still making each other laugh, still as in love as we are today. Our love story spanning decades."
    }
  ];

  return (
    <div ref={containerRef} className="min-h-screen bg-gradient-to-br from-indigo-100 via-purple-100 to-pink-100 py-16 px-4 relative overflow-hidden">
      {/* Animated Background Elements */}
      <motion.div
        style={{ y, opacity }}
        className="absolute inset-0 pointer-events-none"
      >
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute text-purple-200 opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -30, 0],
              rotate: [0, 360],
              scale: [0.8, 1.2, 0.8],
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          >
            <Star size={20 + Math.random() * 15} fill="currentColor" />
          </motion.div>
        ))}
      </motion.div>

      <div className="max-w-6xl mx-auto relative z-10">
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600 mb-4">
            Our Dreams Together
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Every dream I have includes you, Roshni. Here's to our beautiful future together ✨💕
          </p>
        </motion.div>

        {/* Dreams Timeline */}
        <div className="space-y-24">
          {dreams.map((dream, index) => (
            <motion.div
              key={dream.id}
              initial={{ opacity: 0, x: index % 2 === 0 ? -100 : 100 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              className={`flex items-center ${
                index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
              } gap-8`}
            >
              {/* Content */}
              <div className={`flex-1 ${index % 2 === 0 ? 'text-right' : 'text-left'}`}>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl"
                >
                  <h3 className="text-3xl font-dancing-script text-gray-800 mb-4">
                    {dream.title}
                  </h3>
                  <p className="text-gray-600 text-lg leading-relaxed mb-4">
                    {dream.description}
                  </p>
                  <p className="text-gray-500 leading-relaxed">
                    {dream.details}
                  </p>
                </motion.div>
              </div>

              {/* Icon */}
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                className={`w-24 h-24 rounded-full bg-gradient-to-r ${dream.color} flex items-center justify-center text-white shadow-xl flex-shrink-0`}
              >
                {dream.icon}
              </motion.div>

              {/* Spacer for alternating layout */}
              <div className="flex-1"></div>
            </motion.div>
          ))}
        </div>

        {/* Final Message */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="text-center mt-24"
        >
          <div className="bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl max-w-4xl mx-auto">
            <motion.div
              animate={{ scale: [1, 1.05, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="mb-8"
            >
              <Heart className="w-16 h-16 text-pink-500 mx-auto" fill="currentColor" />
            </motion.div>
            
            <h3 className="text-4xl font-dancing-script text-gray-800 mb-6">
              Forever and Always
            </h3>
            
            <p className="text-xl text-gray-600 leading-relaxed mb-8 max-w-3xl mx-auto">
              Every single one of these dreams feels possible because I have you by my side, Roshni. 
              You make me believe in forever, in happily ever after, in love that lasts a lifetime. 
              I can't wait to turn every one of these dreams into our beautiful reality.
            </p>
            
            <div className="flex justify-center space-x-2 mb-6">
              {[...Array(9)].map((_, i) => (
                <Heart
                  key={i}
                  className="w-4 h-4 text-pink-400 animate-pulse"
                  fill="currentColor"
                  style={{ animationDelay: `${i * 0.2}s` }}
                />
              ))}
            </div>
            
            <p className="text-2xl font-dancing-script text-pink-600">
              With all my love, today and always ❤️
            </p>
            <p className="text-lg text-gray-500 mt-2">
              - Your Saurabh
            </p>
          </div>
        </motion.div>

        {/* Floating Elements */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {[...Array(10)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -100, 0],
                x: [0, Math.random() * 50 - 25, 0],
                rotate: [0, 360],
                scale: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 6 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 4,
              }}
            >
              <div className="text-pink-300 opacity-40">
                {[<Heart />, <Star />, <Crown />][Math.floor(Math.random() * 3)]}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FutureDreams;
