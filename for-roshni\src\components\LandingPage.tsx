'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Heart, ChevronDown, Sparkles, Star } from 'lucide-react';
import ParticleBackground from './ParticleBackground';
import dynamic from 'next/dynamic';

// Dynamically import the 3D card to avoid SSR issues
const FloatingBirthdayCard = dynamic(() => import('./FloatingBirthdayCard'), {
  ssr: false,
  loading: () => null
});

const LandingPage = () => {
  const [currentText, setCurrentText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showCursor, setShowCursor] = useState(true);
  const [hasInteracted, setHasInteracted] = useState(false);
  const [showMainContent, setShowMainContent] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [0, -200]);
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);

  const messages = [
    "Hi Roshni... 💖",
    "Happy Birthday, My Love... 🎂",
    "I made this just for you.",
    "A world where only our love exists...",
    "Click anywhere and step into the magic."
  ];

  useEffect(() => {
    if (currentIndex < messages.length) {
      const message = messages[currentIndex];
      let charIndex = 0;

      const typeInterval = setInterval(() => {
        if (charIndex <= message.length) {
          setCurrentText(message.slice(0, charIndex));
          charIndex++;
        } else {
          clearInterval(typeInterval);
          setTimeout(() => {
            if (currentIndex < messages.length - 1) {
              setCurrentIndex(currentIndex + 1);
              setCurrentText('');
            } else {
              setShowMainContent(true);
            }
          }, 2000);
        }
      }, 80);

      return () => clearInterval(typeInterval);
    }
  }, [currentIndex]);

  const handleInteraction = () => {
    if (!hasInteracted) {
      setHasInteracted(true);
      // Trigger audio or other effects here
    }
  };

  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 500);

    return () => clearInterval(cursorInterval);
  }, []);

  const scrollToNext = () => {
    const nextSection = document.getElementById('celebration');
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div
      ref={containerRef}
      id="landing"
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"
      onClick={handleInteraction}
    >
      {/* Cinematic Background */}
      <div className="absolute inset-0">
        {/* Radial Glow */}
        <div className="absolute inset-0 bg-gradient-radial from-pink-500/20 via-purple-500/10 to-transparent" />

        {/* Animated Stars */}
        <div className="absolute inset-0">
          {[...Array(100)].map((_, i) => (
            <motion.div
              key={`star-${i}`}
              className="absolute w-1 h-1 bg-white rounded-full"
              style={{
                left: `${(i * 17 + 23) % 100}%`,
                top: `${(i * 23 + 17) % 100}%`,
              }}
              animate={{
                opacity: [0.3, 1, 0.3],
                scale: [0.5, 1.2, 0.5],
              }}
              transition={{
                duration: 2 + (i % 3),
                repeat: Infinity,
                delay: i * 0.05,
              }}
            />
          ))}
        </div>

        {/* Floating Rose Petals */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(15)].map((_, i) => (
            <motion.div
              key={`petal-${i}`}
              className="absolute"
              style={{
                left: `${(i * 13 + 31) % 100}%`,
                top: `${(i * 19 + 41) % 100}%`,
              }}
              animate={{
                y: [0, -100, -200],
                x: [0, (i % 2 === 0 ? 50 : -50), 0],
                rotate: [0, 360, 720],
                opacity: [0, 0.6, 0],
              }}
              transition={{
                duration: 8 + (i % 4),
                repeat: Infinity,
                delay: i * 0.8,
                ease: "easeInOut",
              }}
            >
              <div
                className="w-3 h-6 rounded-full blur-sm"
                style={{
                  background: `linear-gradient(45deg, #ff6b9d, #f368e0)`,
                  transform: `rotate(${i * 45}deg)`,
                }}
              />
            </motion.div>
          ))}
        </div>

        {/* Glowing Hearts */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(12)].map((_, i) => (
            <motion.div
              key={`glow-heart-${i}`}
              className="absolute text-pink-400"
              style={{
                left: `${(i * 29 + 37) % 100}%`,
                top: `${(i * 31 + 43) % 100}%`,
              }}
              animate={{
                y: [0, -60, 0],
                scale: [0.5, 1.5, 0.5],
                opacity: [0.2, 0.8, 0.2],
              }}
              transition={{
                duration: 6 + (i % 3),
                repeat: Infinity,
                delay: i * 0.5,
              }}
            >
              <Heart
                size={20 + (i % 3) * 10}
                fill="currentColor"
                className="drop-shadow-lg filter blur-[0.5px]"
              />
            </motion.div>
          ))}
        </div>

        {/* Sparkles */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={`sparkle-${i}`}
              className="absolute text-yellow-300"
              style={{
                left: `${(i * 11 + 47) % 100}%`,
                top: `${(i * 13 + 53) % 100}%`,
              }}
              animate={{
                scale: [0, 1, 0],
                rotate: [0, 180, 360],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: i * 0.3,
              }}
            >
              <Sparkles size={8 + (i % 2) * 4} />
            </motion.div>
          ))}
        </div>
      </div>

      {/* Roshni's Silhouette */}
      <motion.div
        style={{ y, opacity }}
        className="absolute right-10 top-1/2 transform -translate-y-1/2 hidden lg:block"
        initial={{ opacity: 0, x: 100 }}
        animate={{ opacity: 0.6, x: 0 }}
        transition={{ duration: 2, delay: 1 }}
      >
        <div className="relative">
          {/* Silhouette */}
          <div className="w-64 h-96 bg-gradient-to-b from-pink-500/30 to-purple-500/30 rounded-full blur-sm" />

          {/* Glow Effect */}
          <div className="absolute inset-0 bg-gradient-to-b from-pink-400/20 to-purple-400/20 rounded-full blur-xl scale-110" />

          {/* Floating Candles around her */}
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={`candle-${i}`}
              className="absolute w-2 h-8 bg-gradient-to-t from-yellow-400 to-orange-300 rounded-sm"
              style={{
                left: `${20 + Math.cos((i * 60) * Math.PI / 180) * 80}px`,
                top: `${50 + Math.sin((i * 60) * Math.PI / 180) * 80}px`,
              }}
              animate={{
                y: [0, -10, 0],
                opacity: [0.7, 1, 0.7],
              }}
              transition={{
                duration: 2 + (i % 2),
                repeat: Infinity,
                delay: i * 0.3,
              }}
            >
              {/* Flame */}
              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-1 h-2 bg-orange-400 rounded-full blur-[1px]" />
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Floating 3D Birthday Card */}
      {showMainContent && <FloatingBirthdayCard />}

      {/* Main Content */}
      <div className="text-center z-20 px-4 max-w-4xl mx-auto relative">
        {/* Typewriter Text */}
        <div className="min-h-[200px] flex items-center justify-center mb-12">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="relative"
          >
            {/* Glow Effect Behind Text */}
            <div className="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-500/20 blur-3xl rounded-full" />

            <div className="relative z-10 text-2xl md:text-4xl font-medium text-white leading-relaxed">
              <motion.span
                key={currentIndex}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="inline-block"
                style={{
                  textShadow: '0 0 20px rgba(236, 72, 153, 0.5)',
                }}
              >
                {currentText}
                {showCursor && (
                  <motion.span
                    animate={{ opacity: [1, 0, 1] }}
                    transition={{ duration: 0.8, repeat: Infinity }}
                    className="inline-block w-1 h-8 bg-pink-400 ml-2"
                  />
                )}
              </motion.span>
            </div>
          </motion.div>
        </div>

        {/* Main Title (appears after typewriter) */}
        {showMainContent && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
            className="mb-16"
          >
            <motion.h1
              className="text-6xl md:text-8xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-pink-400 via-purple-400 to-rose-400 mb-8 leading-tight"
              animate={{
                backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "linear"
              }}
              style={{
                backgroundSize: '200% 200%',
                textShadow: '0 0 40px rgba(236, 72, 153, 0.3)',
              }}
            >
              Happy Birthday
            </motion.h1>

            <motion.h2
              className="text-4xl md:text-6xl font-dancing-script text-pink-300 mb-6"
              animate={{
                textShadow: [
                  '0 0 20px rgba(236, 72, 153, 0.5)',
                  '0 0 40px rgba(236, 72, 153, 0.8)',
                  '0 0 20px rgba(236, 72, 153, 0.5)',
                ],
              }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              Roshni Jwala
            </motion.h2>

            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
              className="text-lg md:text-xl text-pink-200 max-w-2xl mx-auto leading-relaxed"
            >
              My Beautiful Babu, My Everything ✨
            </motion.p>
          </motion.div>
        )}

        {/* Interaction Prompt */}
        {!hasInteracted && showMainContent && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 2 }}
            className="absolute bottom-32 left-1/2 transform -translate-x-1/2"
          >
            <motion.div
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.7, 1, 0.7],
              }}
              transition={{ duration: 2, repeat: Infinity }}
              className="bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20"
            >
              <p className="text-pink-200 text-sm font-medium">
                Click anywhere to begin the magic ✨
              </p>
            </motion.div>
          </motion.div>
        )}

        {/* Scroll Indicator */}
        {showMainContent && hasInteracted && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 3 }}
            className="absolute bottom-16 left-1/2 transform -translate-x-1/2"
          >
            <button
              onClick={scrollToNext}
              className="group flex flex-col items-center text-pink-300 hover:text-pink-200 transition-colors"
            >
              <span className="text-sm font-medium mb-2">Enter Your Birthday World</span>
              <motion.div
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="relative"
              >
                <ChevronDown size={28} className="group-hover:scale-110 transition-transform" />
                <div className="absolute inset-0 bg-pink-400/30 rounded-full blur-lg scale-150" />
              </motion.div>
            </button>
          </motion.div>
        )}
      </div>

      {/* Cinematic Vignette */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-black/30" />
        <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-black/40" />
      </div>

      {/* Lens Flare Effect */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-32 h-32 rounded-full bg-gradient-to-r from-pink-400/20 to-purple-400/20 blur-3xl"
        animate={{
          scale: [1, 1.5, 1],
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    </div>
  );
};

export default LandingPage;
