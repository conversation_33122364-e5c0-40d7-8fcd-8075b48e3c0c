'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Heart, ChevronDown } from 'lucide-react';
import ParticleBackground from './ParticleBackground';

const LandingPage = () => {
  const [currentText, setCurrentText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showCursor, setShowCursor] = useState(true);

  const messages = [
    "Hi Roshni... 💖",
    "Happy Birthday, Jaan... 🎂",
    "This website is just for you...",
    "To celebrate you... to love you... forever ❤️"
  ];

  useEffect(() => {
    if (currentIndex < messages.length) {
      const message = messages[currentIndex];
      let charIndex = 0;

      const typeInterval = setInterval(() => {
        if (charIndex <= message.length) {
          setCurrentText(message.slice(0, charIndex));
          charIndex++;
        } else {
          clearInterval(typeInterval);
          setTimeout(() => {
            if (currentIndex < messages.length - 1) {
              setCurrentIndex(currentIndex + 1);
              setCurrentText('');
            }
          }, 2000);
        }
      }, 100);

      return () => clearInterval(typeInterval);
    }
  }, [currentIndex]);

  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 500);

    return () => clearInterval(cursorInterval);
  }, []);

  const scrollToNext = () => {
    const nextSection = document.getElementById('celebration');
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div id="landing" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      <ParticleBackground />
      
      {/* Floating Hearts */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute text-pink-400 opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -30, 0],
              rotate: [0, 360],
              scale: [0.8, 1.2, 0.8],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          >
            <Heart size={20 + Math.random() * 20} fill="currentColor" />
          </motion.div>
        ))}
      </div>

      {/* Main Content */}
      <div className="text-center z-10 px-4 max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
          className="mb-8"
        >
          <h1 className="text-6xl md:text-8xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-pink-500 via-purple-500 to-rose-500 mb-4">
            For Roshni
          </h1>
          <div className="text-2xl md:text-3xl text-gray-700 font-medium mb-8">
            My Beautiful Babu ✨
          </div>
        </motion.div>

        {/* Typewriter Effect */}
        <div className="min-h-[120px] flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5 }}
            className="text-xl md:text-2xl text-gray-600 font-medium"
          >
            <span className="inline-block">
              {currentText}
              {showCursor && (
                <span className="inline-block w-0.5 h-6 bg-pink-500 ml-1 animate-pulse" />
              )}
            </span>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        {currentIndex >= messages.length - 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 2 }}
            className="mt-16"
          >
            <button
              onClick={scrollToNext}
              className="group flex flex-col items-center text-pink-500 hover:text-pink-600 transition-colors"
            >
              <span className="text-lg font-medium mb-2">Scroll Down</span>
              <motion.div
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <ChevronDown size={32} className="group-hover:scale-110 transition-transform" />
              </motion.div>
            </button>
          </motion.div>
        )}
      </div>

      {/* Background Music Control */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 3 }}
        className="absolute bottom-8 right-8 z-20"
      >
        <button className="bg-white/20 backdrop-blur-sm rounded-full p-3 text-pink-500 hover:bg-white/30 transition-all">
          <Heart size={24} className="animate-heartbeat" />
        </button>
      </motion.div>
    </div>
  );
};

export default LandingPage;
