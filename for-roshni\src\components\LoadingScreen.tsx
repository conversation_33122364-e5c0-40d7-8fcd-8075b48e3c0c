'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Heart, Sparkles } from 'lucide-react';

interface LoadingScreenProps {
  onLoadingComplete: () => void;
}

const LoadingScreen = ({ onLoadingComplete }: LoadingScreenProps) => {
  const [progress, setProgress] = useState(0);
  const [currentMessage, setCurrentMessage] = useState(0);

  const loadingMessages = [
    "Preparing your surprise, Roshni...",
    "Sprinkling some magic ✨",
    "Adding extra love 💕",
    "Almost ready, beautiful...",
    "Welcome to your special day! 🎂"
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setTimeout(onLoadingComplete, 1000);
          return 100;
        }
        return prev + 2;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [onLoadingComplete]);

  useEffect(() => {
    const messageInterval = setInterval(() => {
      setCurrentMessage(prev => (prev + 1) % loadingMessages.length);
    }, 1500);

    return () => clearInterval(messageInterval);
  }, []);

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 1 }}
        className="fixed inset-0 bg-gradient-to-br from-pink-100 via-purple-100 to-rose-100 flex items-center justify-center z-50"
      >
        {/* Background Animation */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute text-pink-300 opacity-20"
              style={{
                left: `${(i * 17 + 23) % 100}%`,
                top: `${(i * 23 + 17) % 100}%`,
              }}
              animate={{
                y: [0, -100, 0],
                rotate: [0, 360],
                scale: [0.5, 1.5, 0.5],
              }}
              transition={{
                duration: 4 + (i % 3),
                repeat: Infinity,
                delay: i * 0.3,
              }}
            >
              <Heart size={15 + (i % 3) * 8} fill="currentColor" />
            </motion.div>
          ))}
        </div>

        <div className="text-center relative z-10 max-w-md mx-auto px-8">
          {/* Main Logo/Title */}
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="mb-12"
          >
            <motion.div
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ 
                duration: 2, 
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full flex items-center justify-center shadow-2xl"
            >
              <Heart className="w-12 h-12 text-white" fill="currentColor" />
            </motion.div>
            
            <h1 className="text-4xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-purple-600 mb-2">
              For Roshni
            </h1>
            <p className="text-gray-600 text-lg">
              A Birthday Gift from Saurabh ❤️
            </p>
          </motion.div>

          {/* Loading Messages */}
          <div className="mb-8 h-16 flex items-center justify-center">
            <AnimatePresence mode="wait">
              <motion.p
                key={currentMessage}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className="text-gray-600 text-lg font-medium"
              >
                {loadingMessages[currentMessage]}
              </motion.p>
            </AnimatePresence>
          </div>

          {/* Progress Bar */}
          <div className="mb-8">
            <div className="w-full bg-white/50 rounded-full h-3 shadow-inner overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-pink-400 to-purple-500 rounded-full shadow-lg"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              />
            </div>
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="text-gray-500 text-sm mt-2"
            >
              {progress}% Complete
            </motion.p>
          </div>

          {/* Floating Elements */}
          <div className="relative">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute text-pink-400"
                style={{
                  left: `${20 + i * 15}%`,
                  top: `${Math.random() * 40}px`,
                }}
                animate={{
                  y: [0, -20, 0],
                  rotate: [0, 360],
                  scale: [0.8, 1.2, 0.8],
                }}
                transition={{
                  duration: 2 + Math.random(),
                  repeat: Infinity,
                  delay: i * 0.3,
                }}
              >
                <Sparkles size={16} />
              </motion.div>
            ))}
          </div>

          {/* Completion Message */}
          {progress === 100 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="mt-8"
            >
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
                className="text-2xl font-dancing-script text-pink-600"
              >
                Ready! Welcome to your magical day! ✨
              </motion.div>
            </motion.div>
          )}
        </div>

        {/* Ambient Glow Effect */}
        <div className="absolute inset-0 bg-gradient-radial from-pink-200/30 via-transparent to-transparent pointer-events-none" />
      </motion.div>
    </AnimatePresence>
  );
};

export default LoadingScreen;
