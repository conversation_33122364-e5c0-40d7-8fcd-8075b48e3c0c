'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Mail, Heart, Sparkles } from 'lucide-react';

const LoveLetter = () => {
  const [isEnvelopeOpen, setIsEnvelopeOpen] = useState(false);
  const [showLetter, setShowLetter] = useState(false);

  const openEnvelope = () => {
    setIsEnvelopeOpen(true);
    setTimeout(() => setShowLetter(true), 1000);
  };

  const letterContent = `My Dearest Roshni,

As I write this letter, my heart is overflowing with love for you. You've brought so much color, chaos, warmth, and peace into my life all at once, and I wouldn't have it any other way.

You are my brightest light when everything feels dark, my calmest comfort when the world gets overwhelming, and my biggest blessing when I count all the good things in my life.

Every morning I wake up grateful that I get to love you, and every night I fall asleep with a smile knowing you're mine. Your laugh is my favorite sound, your smile is my favorite sight, and your love is my favorite feeling.

You make the ordinary moments extraordinary just by being in them. Whether we're having deep conversations at 2 AM, sharing silly memes, or just sitting in comfortable silence, every moment with you feels like a gift.

On your special day, I want you to know that you are loved beyond measure. You are cherished, adored, and celebrated not just today, but every single day.

You are my person, my partner, my best friend, and my greatest love. Thank you for being exactly who you are - beautiful, kind, funny, smart, and absolutely perfect in every way.

I love you more than words can say, more than actions can show, and more than time can measure.

Happy Birthday, my beautiful Babu. Here's to many more years of loving you, laughing with you, and building our beautiful life together.

Forever and always yours,
Saurabh ❤️

P.S. - You're stuck with me forever now, so I hope you're ready for a lifetime of my terrible jokes and endless love! 😘`;

  return (
    <div id="letter" className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 flex items-center justify-center py-16 px-4">
      <div className="max-w-4xl mx-auto text-center">
        <motion.h2
          initial={{ opacity: 0, y: -50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-red-600 to-pink-600 mb-8"
        >
          A Letter For You
        </motion.h2>

        <motion.p
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="text-xl text-gray-600 mb-12"
        >
          From my heart to yours, Roshni ✉️💕
        </motion.p>

        {!isEnvelopeOpen ? (
          // Envelope
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.8 }}
            className="relative mx-auto cursor-pointer"
            style={{ width: '400px', height: '280px' }}
            onClick={openEnvelope}
          >
            {/* Envelope Body */}
            <div className="absolute inset-0 bg-gradient-to-br from-red-100 to-pink-100 rounded-lg shadow-2xl border-2 border-red-200">
              {/* Envelope Flap */}
              <motion.div
                className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-br from-red-200 to-pink-200 origin-top"
                style={{
                  clipPath: 'polygon(0 0, 50% 70%, 100% 0)',
                }}
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                {/* Wax Seal */}
                <div className="absolute top-16 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg">
                  <Heart className="w-6 h-6 text-white" fill="currentColor" />
                </div>
              </motion.div>

              {/* Envelope Content Preview */}
              <div className="absolute bottom-8 left-8 right-8 text-center">
                <Mail className="w-12 h-12 text-red-400 mx-auto mb-4" />
                <p className="text-red-600 font-medium text-lg">For My Beautiful Roshni</p>
                <p className="text-red-500 text-sm mt-2">Click to open ✨</p>
              </div>
            </div>

            {/* Floating Hearts */}
            <div className="absolute inset-0 pointer-events-none">
              {[...Array(8)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute text-red-300 opacity-60"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                  }}
                  animate={{
                    y: [0, -20, 0],
                    rotate: [0, 360],
                    scale: [0.8, 1.2, 0.8],
                  }}
                  transition={{
                    duration: 3 + Math.random() * 2,
                    repeat: Infinity,
                    delay: Math.random() * 2,
                  }}
                >
                  <Heart size={12 + Math.random() * 8} fill="currentColor" />
                </motion.div>
              ))}
            </div>
          </motion.div>
        ) : (
          // Opened Envelope Animation
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="relative mx-auto"
            style={{ width: '400px', height: '280px' }}
          >
            {/* Envelope Body (opened) */}
            <div className="absolute inset-0 bg-gradient-to-br from-red-100 to-pink-100 rounded-lg shadow-2xl border-2 border-red-200">
              {/* Opened Flap */}
              <motion.div
                className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-br from-red-200 to-pink-200 origin-top"
                style={{
                  clipPath: 'polygon(0 0, 50% 70%, 100% 0)',
                }}
                animate={{ rotateX: -180 }}
                transition={{ duration: 1, ease: "easeInOut" }}
              />
            </div>
          </motion.div>
        )}

        {/* Letter Content */}
        <AnimatePresence>
          {showLetter && (
            <motion.div
              initial={{ opacity: 0, y: 100 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, ease: "easeOut" }}
              className="mt-16 max-w-3xl mx-auto"
            >
              <div className="bg-white rounded-2xl shadow-2xl p-8 md:p-12 border border-red-100">
                {/* Letter Header */}
                <div className="text-center mb-8">
                  <Sparkles className="w-8 h-8 text-red-400 mx-auto mb-4" />
                  <h3 className="text-3xl font-dancing-script text-red-600 mb-2">
                    My Love Letter to You
                  </h3>
                  <div className="w-24 h-0.5 bg-gradient-to-r from-red-300 to-pink-300 mx-auto"></div>
                </div>

                {/* Letter Body */}
                <div className="text-left space-y-4 text-gray-700 leading-relaxed">
                  {letterContent.split('\n\n').map((paragraph, index) => (
                    <motion.p
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.3, duration: 0.6 }}
                      className={`${
                        paragraph.includes('My Dearest Roshni') 
                          ? 'font-dancing-script text-xl text-red-600' 
                          : paragraph.includes('Forever and always yours')
                          ? 'font-dancing-script text-lg text-red-600 text-right mt-8'
                          : paragraph.includes('P.S.')
                          ? 'text-sm text-gray-500 italic'
                          : ''
                      }`}
                    >
                      {paragraph}
                    </motion.p>
                  ))}
                </div>

                {/* Letter Footer */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 2 }}
                  className="text-center mt-12"
                >
                  <div className="flex justify-center space-x-2 mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Heart
                        key={i}
                        className="w-4 h-4 text-red-400 animate-pulse"
                        fill="currentColor"
                        style={{ animationDelay: `${i * 0.2}s` }}
                      />
                    ))}
                  </div>
                  <p className="text-red-500 font-medium">
                    With all my love, today and always ❤️
                  </p>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default LoveLetter;
