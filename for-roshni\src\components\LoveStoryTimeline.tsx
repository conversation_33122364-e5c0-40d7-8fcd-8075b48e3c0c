'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Heart, MessageCircle, Camera, Star, Calendar, MapPin } from 'lucide-react';

interface TimelineEvent {
  id: number;
  title: string;
  date: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  image?: string;
  details: string;
}

const LoveStoryTimeline = () => {
  const [selectedEvent, setSelectedEvent] = useState<TimelineEvent | null>(null);

  const timelineEvents: TimelineEvent[] = [
    {
      id: 1,
      title: "First Chat",
      date: "The Beginning",
      description: "When our love story started with a simple message...",
      icon: <MessageCircle className="w-6 h-6" />,
      color: "from-pink-400 to-rose-500",
      details: "That first message changed everything. I never knew a simple 'Hi' could lead to the most beautiful love story. From that moment, I knew you were special, Roshni. ❤️"
    },
    {
      id: 2,
      title: "First Date",
      date: "A Magical Day",
      description: "The day I fell even deeper in love with you...",
      icon: <Heart className="w-6 h-6" />,
      color: "from-purple-400 to-pink-500",
      details: "Our first date was perfect. Your smile, your laugh, the way you looked at me - everything about that day was magical. I knew I wanted to spend forever making you smile like that. 💕"
    },
    {
      id: 3,
      title: "First Fight",
      date: "Growing Stronger",
      description: "Even our fights made us stronger...",
      icon: <Star className="w-6 h-6" />,
      color: "from-yellow-400 to-orange-500",
      details: "Our first fight taught us so much about each other. It showed me how much you care, how passionate you are, and how we can work through anything together. We came out stronger and more in love. 💪❤️"
    },
    {
      id: 4,
      title: "Most Beautiful Memory",
      date: "Unforgettable Moment",
      description: "The moment that's etched in my heart forever...",
      icon: <Camera className="w-6 h-6" />,
      color: "from-blue-400 to-purple-500",
      details: "That sunset evening when you rested your head on my shoulder and said you felt safe with me. The way the golden light touched your face, your peaceful smile - it's the most beautiful memory I have. 🌅💖"
    },
    {
      id: 5,
      title: "Cute Selfie Moments",
      date: "Capturing Love",
      description: "All those silly, beautiful moments we captured...",
      icon: <Camera className="w-6 h-6" />,
      color: "from-green-400 to-blue-500",
      details: "Every selfie we take tells a story. Your goofy faces, our matching expressions, the way you always look perfect even when you think you don't - these photos are treasures of our love. 📸✨"
    },
    {
      id: 6,
      title: "And We're Just Getting Started...",
      date: "Forever & Always",
      description: "Our love story continues to grow every day...",
      icon: <Heart className="w-6 h-6" />,
      color: "from-red-400 to-pink-500",
      details: "This is just the beginning, my love. Every day with you is a new chapter in our beautiful story. I can't wait to create a million more memories with you, Roshni. Forever yours, Saurabh. 💕♾️"
    }
  ];

  return (
    <div id="timeline" className="min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 py-16 px-4">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-purple-600 mb-4">
            Our Love Story
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Every moment with you has been a beautiful chapter in our love story, Babu ✨
          </p>
        </motion.div>

        {/* Timeline */}
        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-pink-300 to-purple-300 rounded-full"></div>

          {timelineEvents.map((event, index) => (
            <motion.div
              key={event.id}
              initial={{ opacity: 0, x: index % 2 === 0 ? -100 : 100 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              className={`relative flex items-center mb-16 ${
                index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
              }`}
            >
              {/* Content */}
              <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  onClick={() => setSelectedEvent(event)}
                  className="bg-white rounded-xl p-6 shadow-lg cursor-pointer hover:shadow-xl transition-all duration-300"
                >
                  <h3 className="text-2xl font-bold text-gray-800 mb-2">{event.title}</h3>
                  <p className="text-pink-600 font-medium mb-3">{event.date}</p>
                  <p className="text-gray-600 leading-relaxed">{event.description}</p>
                  <div className="mt-4 text-pink-500 text-sm font-medium">
                    Click to read more ✨
                  </div>
                </motion.div>
              </div>

              {/* Timeline Node */}
              <div className="absolute left-1/2 transform -translate-x-1/2 z-10">
                <motion.div
                  whileHover={{ scale: 1.2 }}
                  className={`w-16 h-16 rounded-full bg-gradient-to-r ${event.color} flex items-center justify-center text-white shadow-lg cursor-pointer`}
                  onClick={() => setSelectedEvent(event)}
                >
                  {event.icon}
                </motion.div>
              </div>

              {/* Empty space for alternating layout */}
              <div className="w-5/12"></div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Modal for detailed view */}
      <AnimatePresence>
        {selectedEvent && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedEvent(null)}
          >
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              className="bg-white rounded-2xl p-8 max-w-lg mx-auto shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center">
                <div className={`w-20 h-20 rounded-full bg-gradient-to-r ${selectedEvent.color} flex items-center justify-center text-white mx-auto mb-6`}>
                  {selectedEvent.icon}
                </div>
                <h3 className="text-3xl font-dancing-script text-gray-800 mb-2">
                  {selectedEvent.title}
                </h3>
                <p className="text-pink-600 font-medium mb-4">{selectedEvent.date}</p>
                <p className="text-gray-600 leading-relaxed mb-6">
                  {selectedEvent.details}
                </p>
                <button
                  onClick={() => setSelectedEvent(null)}
                  className="bg-gradient-to-r from-pink-500 to-purple-500 text-white px-8 py-3 rounded-full hover:from-pink-600 hover:to-purple-600 transition-all font-medium"
                >
                  Close ❤️
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LoveStoryTimeline;
