'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Camera, Heart, X, Play, Pause } from 'lucide-react';

interface Photo {
  id: number;
  src: string;
  caption: string;
  description: string;
  date: string;
}

const PhotoGallery = () => {
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
  const [isSlideshow, setIsSlideshow] = useState(false);
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);

  // Placeholder photos - in a real implementation, these would be actual photos
  const photos: Photo[] = [
    {
      id: 1,
      src: "/api/placeholder/300/400",
      caption: "Cutest Smile",
      description: "This is my favorite photo of you, <PERSON><PERSON><PERSON>. Your smile here just melts my heart every single time I look at it. ❤️",
      date: "A beautiful day"
    },
    {
      id: 2,
      src: "/api/placeholder/300/400",
      caption: "Best Hug",
      description: "The warmest, most comforting hug ever. I felt so safe and loved in this moment with you, my beautiful babu. 🤗",
      date: "Perfect moment"
    },
    {
      id: 3,
      src: "/api/placeholder/300/400",
      caption: "My Princess",
      description: "You look absolutely stunning here! Like a real princess, which you are to me every single day. 👑✨",
      date: "Magical evening"
    },
    {
      id: 4,
      src: "/api/placeholder/300/400",
      caption: "Silly Us",
      description: "I love how goofy we can be together. These silly moments are some of my most treasured memories with you! 😄",
      date: "Fun times"
    },
    {
      id: 5,
      src: "/api/placeholder/300/400",
      caption: "Beautiful Eyes",
      description: "Your eyes in this photo... I could get lost in them forever. They hold so much love, warmth, and beauty. 👀💕",
      date: "Dreamy afternoon"
    },
    {
      id: 6,
      src: "/api/placeholder/300/400",
      caption: "Together Forever",
      description: "This photo represents everything I want - you and me, together, happy, and in love. Forever and always. 💑",
      date: "Our future"
    }
  ];

  const startSlideshow = () => {
    setIsSlideshow(true);
    setCurrentSlideIndex(0);
  };

  const stopSlideshow = () => {
    setIsSlideshow(false);
  };

  const nextSlide = () => {
    setCurrentSlideIndex((prev) => (prev + 1) % photos.length);
  };

  const prevSlide = () => {
    setCurrentSlideIndex((prev) => (prev - 1 + photos.length) % photos.length);
  };

  return (
    <div id="gallery" className="min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 py-16 px-4">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-orange-600 to-red-600 mb-4">
            Our Beautiful Memories
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            Every photo tells a story of our love, Roshni 📸💕
          </p>
          
          <motion.button
            onClick={startSlideshow}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-3 rounded-full hover:from-orange-600 hover:to-red-600 transition-all font-medium flex items-center space-x-2 mx-auto"
          >
            <Play className="w-5 h-5" />
            <span>Start Slideshow</span>
          </motion.button>
        </motion.div>

        {/* Photo Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {photos.map((photo, index) => (
            <motion.div
              key={photo.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -10, rotate: Math.random() * 6 - 3 }}
              className="cursor-pointer"
              onClick={() => setSelectedPhoto(photo)}
            >
              {/* Polaroid Frame */}
              <div className="bg-white p-4 pb-16 shadow-xl transform rotate-1 hover:rotate-0 transition-all duration-300">
                {/* Photo */}
                <div className="relative overflow-hidden bg-gray-200 aspect-[3/4] mb-4">
                  {/* Placeholder for actual photo */}
                  <div className="w-full h-full bg-gradient-to-br from-pink-200 to-purple-200 flex items-center justify-center">
                    <Camera className="w-16 h-16 text-gray-400" />
                  </div>
                  
                  {/* Hover Overlay */}
                  <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
                    <Heart className="w-8 h-8 text-white opacity-0 hover:opacity-100 transition-opacity" fill="currentColor" />
                  </div>
                </div>
                
                {/* Caption */}
                <div className="text-center">
                  <h3 className="font-dancing-script text-xl text-gray-800 mb-1">
                    {photo.caption}
                  </h3>
                  <p className="text-sm text-gray-500">{photo.date}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Photo Detail Modal */}
      <AnimatePresence>
        {selectedPhoto && !isSlideshow && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedPhoto(null)}
          >
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              className="bg-white rounded-2xl p-8 max-w-2xl mx-auto shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-start mb-6">
                <h3 className="text-3xl font-dancing-script text-gray-800">
                  {selectedPhoto.caption}
                </h3>
                <button
                  onClick={() => setSelectedPhoto(null)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
              
              {/* Photo */}
              <div className="relative overflow-hidden bg-gray-200 aspect-[3/4] mb-6 rounded-lg">
                <div className="w-full h-full bg-gradient-to-br from-pink-200 to-purple-200 flex items-center justify-center">
                  <Camera className="w-24 h-24 text-gray-400" />
                </div>
              </div>
              
              <p className="text-gray-600 leading-relaxed mb-4">
                {selectedPhoto.description}
              </p>
              
              <p className="text-orange-500 font-medium text-center">
                {selectedPhoto.date}
              </p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Slideshow Modal */}
      <AnimatePresence>
        {isSlideshow && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black flex items-center justify-center z-50"
          >
            {/* Controls */}
            <div className="absolute top-8 right-8 flex space-x-4 z-10">
              <button
                onClick={stopSlideshow}
                className="bg-white/20 backdrop-blur-sm rounded-full p-3 text-white hover:bg-white/30 transition-all"
              >
                <Pause className="w-6 h-6" />
              </button>
              <button
                onClick={stopSlideshow}
                className="bg-white/20 backdrop-blur-sm rounded-full p-3 text-white hover:bg-white/30 transition-all"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Navigation */}
            <button
              onClick={prevSlide}
              className="absolute left-8 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm rounded-full p-4 text-white hover:bg-white/30 transition-all z-10"
            >
              ←
            </button>
            <button
              onClick={nextSlide}
              className="absolute right-8 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm rounded-full p-4 text-white hover:bg-white/30 transition-all z-10"
            >
              →
            </button>

            {/* Current Photo */}
            <div className="text-center text-white max-w-4xl mx-auto px-8">
              <motion.div
                key={currentSlideIndex}
                initial={{ opacity: 0, x: 100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -100 }}
                className="mb-8"
              >
                <div className="relative overflow-hidden bg-gray-800 aspect-[3/4] max-h-96 mx-auto mb-6 rounded-lg">
                  <div className="w-full h-full bg-gradient-to-br from-pink-300 to-purple-300 flex items-center justify-center">
                    <Camera className="w-24 h-24 text-gray-600" />
                  </div>
                </div>
                
                <h3 className="text-4xl font-dancing-script mb-4">
                  {photos[currentSlideIndex].caption}
                </h3>
                <p className="text-lg leading-relaxed mb-4 max-w-2xl mx-auto">
                  {photos[currentSlideIndex].description}
                </p>
                <p className="text-orange-300 font-medium">
                  {photos[currentSlideIndex].date}
                </p>
              </motion.div>
            </div>

            {/* Progress Indicators */}
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2">
              {photos.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentSlideIndex(index)}
                  className={`w-3 h-3 rounded-full transition-all ${
                    index === currentSlideIndex ? 'bg-white' : 'bg-white/40'
                  }`}
                />
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PhotoGallery;
