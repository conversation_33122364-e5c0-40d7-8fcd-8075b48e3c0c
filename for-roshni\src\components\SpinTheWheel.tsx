'use client';

import { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { RotateCcw, <PERSON>, Sparkles, Star } from 'lucide-react';

interface WheelSegment {
  id: number;
  text: string;
  category: string;
  color: string;
  icon: React.ReactNode;
}

const SpinTheWheel = () => {
  const [isSpinning, setIsSpinning] = useState(false);
  const [currentRotation, setCurrentRotation] = useState(0);
  const [selectedSegment, setSelectedSegment] = useState<WheelSegment | null>(null);
  const [showResult, setShowResult] = useState(false);

  const wheelSegments: WheelSegment[] = [
    {
      id: 1,
      text: "Your smile lights up my entire world",
      category: "Reason I love you",
      color: "from-pink-400 to-rose-500",
      icon: <Heart className="w-4 h-4" />
    },
    {
      id: 2,
      text: "The way you scrunch your nose when you laugh",
      category: "Cute thing you do",
      color: "from-purple-400 to-pink-500",
      icon: <Sparkles className="w-4 h-4" />
    },
    {
      id: 3,
      text: "Our first kiss under the stars",
      category: "Memory I'll never forget",
      color: "from-blue-400 to-purple-500",
      icon: <Star className="w-4 h-4" />
    },
    {
      id: 4,
      text: "Traveling the world together",
      category: "Dream I have with you",
      color: "from-green-400 to-blue-500",
      icon: <Heart className="w-4 h-4" />
    },
    {
      id: 5,
      text: "You make me want to be a better person",
      category: "Reason I love you",
      color: "from-yellow-400 to-orange-500",
      icon: <Sparkles className="w-4 h-4" />
    },
    {
      id: 6,
      text: "How you steal my hoodies and look adorable",
      category: "Cute thing you do",
      color: "from-red-400 to-pink-500",
      icon: <Heart className="w-4 h-4" />
    },
    {
      id: 7,
      text: "That time you fell asleep on my shoulder",
      category: "Memory I'll never forget",
      color: "from-indigo-400 to-purple-500",
      icon: <Star className="w-4 h-4" />
    },
    {
      id: 8,
      text: "Growing old together and still being silly",
      category: "Dream I have with you",
      color: "from-teal-400 to-blue-500",
      icon: <Sparkles className="w-4 h-4" />
    }
  ];

  const spinWheel = () => {
    if (isSpinning) return;

    setIsSpinning(true);
    setShowResult(false);
    setSelectedSegment(null);

    // Random rotation between 1440 and 2160 degrees (4-6 full rotations)
    const randomRotation = 1440 + Math.random() * 720;
    const newRotation = currentRotation + randomRotation;
    setCurrentRotation(newRotation);

    // Calculate which segment was selected
    const segmentAngle = 360 / wheelSegments.length;
    const normalizedRotation = newRotation % 360;
    const selectedIndex = Math.floor((360 - normalizedRotation + segmentAngle / 2) / segmentAngle) % wheelSegments.length;
    
    setTimeout(() => {
      setIsSpinning(false);
      setSelectedSegment(wheelSegments[selectedIndex]);
      setShowResult(true);
    }, 3000);
  };

  const segmentAngle = 360 / wheelSegments.length;

  return (
    <div id="wheel" className="min-h-screen bg-gradient-to-br from-violet-50 via-purple-50 to-fuchsia-50 py-16 px-4">
      <div className="max-w-4xl mx-auto text-center">
        <motion.h2
          initial={{ opacity: 0, y: -50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600 mb-8"
        >
          Spin the Wheel of Love
        </motion.h2>

        <motion.p
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="text-xl text-gray-600 mb-12"
        >
          Discover sweet reasons why I love you, Babu! 💕
        </motion.p>

        {/* Wheel Container */}
        <div className="relative mx-auto mb-12" style={{ width: '400px', height: '400px' }}>
          {/* Wheel */}
          <motion.div
            className="relative w-full h-full rounded-full shadow-2xl overflow-hidden"
            animate={{ rotate: currentRotation }}
            transition={{
              duration: isSpinning ? 3 : 0,
              ease: isSpinning ? "easeOut" : "linear"
            }}
          >
            {wheelSegments.map((segment, index) => {
              const rotation = index * segmentAngle;
              return (
                <div
                  key={segment.id}
                  className={`absolute w-full h-full bg-gradient-to-r ${segment.color}`}
                  style={{
                    clipPath: `polygon(50% 50%, 50% 0%, ${50 + 50 * Math.cos((segmentAngle * Math.PI) / 180)}% ${50 - 50 * Math.sin((segmentAngle * Math.PI) / 180)}%)`,
                    transform: `rotate(${rotation}deg)`,
                    transformOrigin: 'center'
                  }}
                >
                  <div 
                    className="absolute text-white text-xs font-medium p-2"
                    style={{
                      top: '20%',
                      left: '60%',
                      transform: `rotate(${segmentAngle / 2}deg)`,
                      transformOrigin: 'left center',
                      width: '80px'
                    }}
                  >
                    <div className="flex items-center space-x-1">
                      {segment.icon}
                      <span className="text-[10px] leading-tight">{segment.category}</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </motion.div>

          {/* Center Circle */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-white rounded-full shadow-lg flex items-center justify-center z-10">
            <Heart className="w-8 h-8 text-pink-500" fill="currentColor" />
          </div>

          {/* Pointer */}
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 z-20">
            <div className="w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-white shadow-lg"></div>
          </div>
        </div>

        {/* Spin Button */}
        <motion.button
          onClick={spinWheel}
          disabled={isSpinning}
          whileHover={{ scale: isSpinning ? 1 : 1.05 }}
          whileTap={{ scale: isSpinning ? 1 : 0.95 }}
          className={`
            px-8 py-4 rounded-full text-white font-bold text-lg shadow-lg transition-all duration-300
            ${isSpinning 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'
            }
          `}
        >
          <div className="flex items-center space-x-2">
            <RotateCcw className={`w-5 h-5 ${isSpinning ? 'animate-spin' : ''}`} />
            <span>{isSpinning ? 'Spinning...' : 'Spin the Wheel!'}</span>
          </div>
        </motion.button>

        {/* Result Modal */}
        <AnimatePresence>
          {showResult && selectedSegment && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
              onClick={() => setShowResult(false)}
            >
              <motion.div
                initial={{ scale: 0.5, opacity: 0, rotateY: -90 }}
                animate={{ scale: 1, opacity: 1, rotateY: 0 }}
                exit={{ scale: 0.5, opacity: 0, rotateY: 90 }}
                transition={{ type: "spring", damping: 25, stiffness: 300 }}
                className="bg-white rounded-2xl p-8 max-w-md mx-auto shadow-2xl"
                onClick={(e) => e.stopPropagation()}
              >
                {/* Confetti Effect */}
                <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
                  {[...Array(20)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-2 h-2 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full"
                      style={{
                        left: `${Math.random() * 100}%`,
                        top: '-10px',
                      }}
                      animate={{
                        y: 400,
                        rotate: 360,
                        x: [0, Math.random() * 100 - 50],
                      }}
                      transition={{
                        duration: 2,
                        ease: "easeOut",
                        delay: Math.random() * 0.5,
                      }}
                    />
                  ))}
                </div>

                <div className="text-center relative z-10">
                  <div className={`w-20 h-20 rounded-full bg-gradient-to-r ${selectedSegment.color} flex items-center justify-center text-white mx-auto mb-6`}>
                    {selectedSegment.icon}
                  </div>
                  
                  <h3 className="text-2xl font-dancing-script text-gray-800 mb-2">
                    {selectedSegment.category}
                  </h3>
                  
                  <p className="text-gray-600 leading-relaxed mb-6 text-lg">
                    "{selectedSegment.text}"
                  </p>
                  
                  <div className="flex justify-center space-x-2 mb-6">
                    {[...Array(5)].map((_, i) => (
                      <Heart
                        key={i}
                        className="w-4 h-4 text-pink-400 animate-pulse"
                        fill="currentColor"
                        style={{ animationDelay: `${i * 0.2}s` }}
                      />
                    ))}
                  </div>
                  
                  <button
                    onClick={() => setShowResult(false)}
                    className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-3 rounded-full hover:from-purple-600 hover:to-pink-600 transition-all font-medium"
                  >
                    Aww, I love you too! ❤️
                  </button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default SpinTheWheel;
