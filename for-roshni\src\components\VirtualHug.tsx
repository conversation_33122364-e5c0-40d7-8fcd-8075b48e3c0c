'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Heart, Sparkles, Volume2, VolumeX } from 'lucide-react';

const VirtualHug = () => {
  const [isHugging, setIsHugging] = useState(false);
  const [showHeartbeat, setShowHeartbeat] = useState(false);
  const [audioEnabled, setAudioEnabled] = useState(false);
  const [hugCount, setHugCount] = useState(0);

  const startHug = () => {
    setIsHugging(true);
    setShowHeartbeat(true);
    setHugCount(prev => prev + 1);
    
    // Simulate heartbeat sound effect
    if (audioEnabled) {
      // In a real implementation, you would play actual audio here
      console.log('Playing heartbeat sound...');
    }

    // Reset after 5 seconds
    setTimeout(() => {
      setIsHugging(false);
      setShowHeartbeat(false);
    }, 5000);
  };

  const toggleAudio = () => {
    setAudioEnabled(!audioEnabled);
  };

  return (
    <div id="hug" className="min-h-screen bg-gradient-to-br from-rose-100 via-pink-100 to-purple-100 flex items-center justify-center py-16 px-4 relative overflow-hidden">
      {/* Background Hearts */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute text-pink-300 opacity-20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -50, 0],
              rotate: [0, 360],
              scale: [0.5, 1.5, 0.5],
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          >
            <Heart size={15 + Math.random() * 25} fill="currentColor" />
          </motion.div>
        ))}
      </div>

      <div className="max-w-4xl mx-auto text-center relative z-10">
        <motion.h2
          initial={{ opacity: 0, y: -50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-5xl md:text-7xl font-dancing-script text-transparent bg-clip-text bg-gradient-to-r from-rose-600 to-pink-600 mb-8"
        >
          Missing Me?
        </motion.h2>

        <motion.p
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto"
        >
          Whenever you miss me, just click the button below for a warm virtual hug from your Saurabh 🤗💕
        </motion.p>

        {/* Audio Toggle */}
        <motion.button
          onClick={toggleAudio}
          className="absolute top-0 right-0 bg-white/20 backdrop-blur-sm rounded-full p-3 text-pink-500 hover:bg-white/30 transition-all mb-8"
        >
          {audioEnabled ? <Volume2 className="w-6 h-6" /> : <VolumeX className="w-6 h-6" />}
        </motion.button>

        {/* Hug Counter */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          whileInView={{ opacity: 1, scale: 1 }}
          className="mb-8"
        >
          <p className="text-gray-500 text-lg">
            Virtual hugs given: <span className="text-pink-600 font-bold">{hugCount}</span> 🤗
          </p>
        </motion.div>

        {/* Main Hug Button */}
        <motion.div
          className="relative mb-12"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <motion.button
            onClick={startHug}
            disabled={isHugging}
            className={`
              relative w-64 h-64 rounded-full text-white font-bold text-xl shadow-2xl transition-all duration-500
              ${isHugging 
                ? 'bg-gradient-to-r from-red-400 to-pink-400 cursor-not-allowed' 
                : 'bg-gradient-to-r from-rose-500 to-pink-500 hover:from-rose-600 hover:to-pink-600'
              }
            `}
            animate={isHugging ? {
              scale: [1, 1.1, 1],
              boxShadow: [
                "0 0 0 0 rgba(236, 72, 153, 0.7)",
                "0 0 0 20px rgba(236, 72, 153, 0)",
                "0 0 0 0 rgba(236, 72, 153, 0)"
              ]
            } : {}}
            transition={{
              duration: 1,
              repeat: isHugging ? Infinity : 0,
            }}
          >
            <div className="flex flex-col items-center justify-center h-full">
              <Heart 
                className={`w-16 h-16 mb-4 ${isHugging ? 'animate-pulse' : ''}`} 
                fill="currentColor" 
              />
              <span className="text-lg">
                {isHugging ? 'Hugging...' : 'Click if you miss me'}
              </span>
            </div>

            {/* Ripple Effect */}
            {isHugging && (
              <motion.div
                className="absolute inset-0 rounded-full border-4 border-white"
                animate={{
                  scale: [1, 2, 3],
                  opacity: [1, 0.5, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                }}
              />
            )}
          </motion.button>
        </motion.div>

        {/* Hug Message */}
        <AnimatePresence>
          {isHugging && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl max-w-2xl mx-auto"
            >
              <motion.div
                animate={{ scale: [1, 1.05, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
                className="text-center"
              >
                <Sparkles className="w-12 h-12 text-pink-500 mx-auto mb-4" />
                <h3 className="text-2xl font-dancing-script text-gray-800 mb-4">
                  Here's a tight virtual hug, baby... 🤗
                </h3>
                <p className="text-gray-600 leading-relaxed mb-4">
                  I'm wrapping my arms around you right now, holding you close to my heart. 
                  Feel my love surrounding you, keeping you warm and safe. 
                  You're never alone, Roshni - I'm always here with you, loving you endlessly.
                </p>
                <div className="flex justify-center space-x-2">
                  {[...Array(7)].map((_, i) => (
                    <Heart
                      key={i}
                      className="w-4 h-4 text-pink-400 animate-pulse"
                      fill="currentColor"
                      style={{ animationDelay: `${i * 0.2}s` }}
                    />
                  ))}
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Heartbeat Visualization */}
        <AnimatePresence>
          {showHeartbeat && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 pointer-events-none z-0"
            >
              {/* Heartbeat Particles */}
              {[...Array(30)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2 bg-pink-400 rounded-full"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                  }}
                  animate={{
                    scale: [0, 1, 0],
                    opacity: [0, 1, 0],
                  }}
                  transition={{
                    duration: 1,
                    repeat: Infinity,
                    delay: Math.random() * 2,
                  }}
                />
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Sweet Messages */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6"
        >
          {[
            {
              title: "Always Here",
              message: "No matter how far apart we are, my love reaches you instantly ❤️",
              icon: <Heart className="w-8 h-8" />
            },
            {
              title: "Thinking of You",
              message: "Every moment of every day, you're in my thoughts and in my heart 💭",
              icon: <Sparkles className="w-8 h-8" />
            },
            {
              title: "Forever Yours",
              message: "Distance means nothing when someone means everything to you 💕",
              icon: <Heart className="w-8 h-8" />
            }
          ].map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 + index * 0.2 }}
              className="bg-white/60 backdrop-blur-sm rounded-xl p-6 text-center hover:bg-white/80 transition-all"
            >
              <div className="text-pink-500 mb-4 flex justify-center">
                {item.icon}
              </div>
              <h4 className="font-dancing-script text-xl text-gray-800 mb-2">
                {item.title}
              </h4>
              <p className="text-gray-600 text-sm leading-relaxed">
                {item.message}
              </p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  );
};

export default VirtualHug;
